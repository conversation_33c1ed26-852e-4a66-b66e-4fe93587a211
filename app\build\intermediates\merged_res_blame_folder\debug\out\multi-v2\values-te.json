{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,243,298,405,519", "endColumns": "52,54,106,113,91", "endOffsets": "242,297,404,518,610"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,162,221,332,450", "endColumns": "56,58,110,117,95", "endOffsets": "157,216,327,445,541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4524,4620,8425,8522,8694,8866,8955,9051,9139,9223,9296,9369,9453,9713,10082,10159,10228", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "4615,4702,8517,8617,8778,8950,9046,9134,9218,9291,9364,9448,9538,9785,10154,10223,10340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3774,3876,3984,4086,4187,4293,4400,9878", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3871,3979,4081,4182,4288,4395,4519,9974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,307,417,543,624,719,832,968,1076,1215,1295,1394,1484,1578,1690,1816,1920,2065,2207,2344,2536,2668,2780,2898,3035,3128,3223,3344,3468,3570,3672,3774,3912,4058,4162,4261,4333,4416,4506,4594,4697,4773,4852,4949,5050,5143,5241,5325,5432,5530,5627,5746,5822,5926", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "175,302,412,538,619,714,827,963,1071,1210,1290,1389,1479,1573,1685,1811,1915,2060,2202,2339,2531,2663,2775,2893,3030,3123,3218,3339,3463,3565,3667,3769,3907,4053,4157,4256,4328,4411,4501,4589,4692,4768,4847,4944,5045,5138,5236,5320,5427,5525,5622,5741,5817,5921,6014"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3286,3411,3538,3648,4707,4788,4883,4996,5132,5240,5379,5459,5558,5648,5742,5854,5980,6084,6229,6371,6508,6700,6832,6944,7062,7199,7292,7387,7508,7632,7734,7836,7938,8076,8222,8326,8622,8783,9543,9790,9979,10345,10421,10500,10597,10698,10791,10889,10973,11080,11178,11275,11394,11470,11574", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "3406,3533,3643,3769,4783,4878,4991,5127,5235,5374,5454,5553,5643,5737,5849,5975,6079,6224,6366,6503,6695,6827,6939,7057,7194,7287,7382,7503,7627,7729,7831,7933,8071,8217,8321,8420,8689,8861,9628,9873,10077,10416,10495,10592,10693,10786,10884,10968,11075,11173,11270,11389,11465,11569,11662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,2920"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "546,663,775,886,976,1081,1200,1278,1355,1446,1538,1633,1727,1828,1921,2016,2111,2202,2293,2375,2489,2591,2688,2803,2906,3021,3183,9633", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "658,770,881,971,1076,1195,1273,1350,1441,1533,1628,1722,1823,1916,2011,2106,2197,2288,2370,2484,2586,2683,2798,2901,3016,3178,3281,9708"}}]}]}