{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "547,647,741,857,942,1042,1155,1233,1309,1400,1493,1586,1680,1774,1867,1962,2060,2151,2242,2321,2429,2536,2632,2745,2848,2949,3102,9224", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "642,736,852,937,1037,1150,1228,1304,1395,1488,1581,1675,1769,1862,1957,2055,2146,2237,2316,2424,2531,2627,2740,2843,2944,3097,3194,9299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,289,403,526", "endColumns": "48,49,113,122,85", "endOffsets": "238,288,402,525,611"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,158,212,330,457", "endColumns": "52,53,117,126,89", "endOffsets": "153,207,325,452,542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,379,489,564,655,764,904,1022,1167,1247,1343,1428,1518,1628,1747,1848,1971,2089,2221,2390,2516,2629,2748,2864,2950,3044,3159,3288,3382,3496,3594,3717,3848,3949,4042,4118,4193,4273,4354,4451,4527,4607,4704,4799,4890,4985,5068,5169,5267,5367,5480,5556,5654", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "162,271,374,484,559,650,759,899,1017,1162,1242,1338,1423,1513,1623,1742,1843,1966,2084,2216,2385,2511,2624,2743,2859,2945,3039,3154,3283,3377,3491,3589,3712,3843,3944,4037,4113,4188,4268,4349,4446,4522,4602,4699,4794,4885,4980,5063,5164,5262,5362,5475,5551,5649,5744"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3199,3311,3420,3523,4532,4607,4698,4807,4947,5065,5210,5290,5386,5471,5561,5671,5790,5891,6014,6132,6264,6433,6559,6672,6791,6907,6993,7087,7202,7331,7425,7539,7637,7760,7891,7992,8279,8437,9144,9374,9556,9918,9994,10074,10171,10266,10357,10452,10535,10636,10734,10834,10947,11023,11121", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "3306,3415,3518,3628,4602,4693,4802,4942,5060,5205,5285,5381,5466,5556,5666,5785,5886,6009,6127,6259,6428,6554,6667,6786,6902,6988,7082,7197,7326,7420,7534,7632,7755,7886,7987,8080,8350,8507,9219,9450,9648,9989,10069,10166,10261,10352,10447,10530,10631,10729,10829,10942,11018,11116,11211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3633,3729,3831,3928,4026,4133,4242,9455", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3724,3826,3923,4021,4128,4237,4355,9551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4360,4452,8085,8180,8355,8512,8589,8678,8767,8849,8914,8979,9060,9304,9653,9731,9798", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4447,4527,8175,8274,8432,8584,8673,8762,8844,8909,8974,9055,9139,9369,9726,9793,9913"}}]}]}