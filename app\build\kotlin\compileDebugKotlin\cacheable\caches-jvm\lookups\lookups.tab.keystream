  Manifest android  
permission android.Manifest  CAMERA android.Manifest.permission  Activity android.app  Application android.app  Bundle android.app.Activity  
MeasureApp android.app.Activity  MeasureTheme android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  fillMaxSize android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  window android.app.Activity  Context android.content  Bundle android.content.Context  
MeasureApp android.content.Context  MeasureTheme android.content.Context  Modifier android.content.Context  SENSOR_SERVICE android.content.Context  Scaffold android.content.Context  display android.content.Context  fillMaxSize android.content.Context  
getDISPLAY android.content.Context  
getDisplay android.content.Context  getSystemService android.content.Context  onCreate android.content.Context  padding android.content.Context  
setContent android.content.Context  
setDisplay android.content.Context  Bundle android.content.ContextWrapper  
MeasureApp android.content.ContextWrapper  MeasureTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  onCreate android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  Sensor android.hardware  SensorEvent android.hardware  SensorEventListener android.hardware  
SensorManager android.hardware  TYPE_ACCELEROMETER android.hardware.Sensor  TYPE_GYROSCOPE android.hardware.Sensor  TYPE_MAGNETIC_FIELD android.hardware.Sensor  getLET android.hardware.Sensor  getLet android.hardware.Sensor  getTYPE android.hardware.Sensor  getType android.hardware.Sensor  let android.hardware.Sensor  setType android.hardware.Sensor  type android.hardware.Sensor  sensor android.hardware.SensorEvent  values android.hardware.SensorEvent  SENSOR_DELAY_GAME android.hardware.SensorManager  getDefaultSensor android.hardware.SensorManager  getOrientation android.hardware.SensorManager  getRotationMatrix android.hardware.SensorManager  registerListener android.hardware.SensorManager  unregisterListener android.hardware.SensorManager  GLES20 android.opengl  
GLSurfaceView android.opengl  GL_CLAMP_TO_EDGE android.opengl.GLES20  GL_COLOR_BUFFER_BIT android.opengl.GLES20  GL_DEPTH_BUFFER_BIT android.opengl.GLES20  	GL_LINEAR android.opengl.GLES20  
GL_TEXTURE_2D android.opengl.GLES20  GL_TEXTURE_MAG_FILTER android.opengl.GLES20  GL_TEXTURE_MIN_FILTER android.opengl.GLES20  GL_TEXTURE_WRAP_S android.opengl.GLES20  GL_TEXTURE_WRAP_T android.opengl.GLES20  
glBindTexture android.opengl.GLES20  glClear android.opengl.GLES20  glClearColor android.opengl.GLES20  
glGenTextures android.opengl.GLES20  glTexParameteri android.opengl.GLES20  
glViewport android.opengl.GLES20  
ARRenderer android.opengl.GLSurfaceView  Boolean android.opengl.GLSurfaceView  Context android.opengl.GLSurfaceView  Float android.opengl.GLSurfaceView  MotionEvent android.opengl.GLSurfaceView  RENDERMODE_CONTINUOUSLY android.opengl.GLSurfaceView  Renderer android.opengl.GLSurfaceView  Unit android.opengl.GLSurfaceView  invoke android.opengl.GLSurfaceView  onPause android.opengl.GLSurfaceView  onResume android.opengl.GLSurfaceView  onTap android.opengl.GLSurfaceView  onTouchEvent android.opengl.GLSurfaceView  setEGLConfigChooser android.opengl.GLSurfaceView  setEGLContextClientVersion android.opengl.GLSurfaceView  setRenderer android.opengl.GLSurfaceView  setWillNotDraw android.opengl.GLSurfaceView  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  MotionEvent android.view  View android.view  Window android.view  Bundle  android.view.ContextThemeWrapper  
MeasureApp  android.view.ContextThemeWrapper  MeasureTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  getROTATION android.view.Display  getRotation android.view.Display  rotation android.view.Display  setRotation android.view.Display  ACTION_DOWN android.view.MotionEvent  action android.view.MotionEvent  	getACTION android.view.MotionEvent  	getAction android.view.MotionEvent  getX android.view.MotionEvent  getY android.view.MotionEvent  	setAction android.view.MotionEvent  setX android.view.MotionEvent  setY android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  
ARRenderer android.view.SurfaceView  Boolean android.view.SurfaceView  Context android.view.SurfaceView  Float android.view.SurfaceView  MotionEvent android.view.SurfaceView  RENDERMODE_CONTINUOUSLY android.view.SurfaceView  Unit android.view.SurfaceView  invoke android.view.SurfaceView  onPause android.view.SurfaceView  onResume android.view.SurfaceView  onTap android.view.SurfaceView  onTouchEvent android.view.SurfaceView  setEGLConfigChooser android.view.SurfaceView  setEGLContextClientVersion android.view.SurfaceView  setRenderer android.view.SurfaceView  setWillNotDraw android.view.SurfaceView  
ARRenderer android.view.View  Boolean android.view.View  Context android.view.View  Float android.view.View  MotionEvent android.view.View  RENDERMODE_CONTINUOUSLY android.view.View  Unit android.view.View  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  invoke android.view.View  isInEditMode android.view.View  onPause android.view.View  onResume android.view.View  onTap android.view.View  onTouchEvent android.view.View  
setContext android.view.View  setEGLConfigChooser android.view.View  setEGLContextClientVersion android.view.View  
setInEditMode android.view.View  setRenderer android.view.View  setWillNotDraw android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
MeasureApp #androidx.activity.ComponentActivity  MeasureTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ARCameraView "androidx.compose.foundation.layout  	AROverlay "androidx.compose.foundation.layout  Add "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  BottomMeasurementControls "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  CalibrationOverlay "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Clear "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  ExperimentalPermissionsApi "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  
LoadingScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  MeasureBlue "androidx.compose.foundation.layout  MeasurementInstructions "androidx.compose.foundation.layout  MeasurementToolbar "androidx.compose.foundation.layout  MeasurementType "androidx.compose.foundation.layout  MeasurementTypeButton "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PermissionRequestScreen "androidx.compose.foundation.layout  QualityIndicator "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Star "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  ToolMenu "androidx.compose.foundation.layout  TopStatusBar "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  ARCameraView +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  BottomMeasurementControls +androidx.compose.foundation.layout.BoxScope  CalibrationOverlay +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  LinearProgressIndicator +androidx.compose.foundation.layout.BoxScope  
LoadingScreen +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  MeasureBlue +androidx.compose.foundation.layout.BoxScope  MeasurementInstructions +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  PermissionRequestScreen +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Settings +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  TopStatusBar +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  getCLIP +androidx.compose.foundation.layout.BoxScope  getClip +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  	isGranted +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Build .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Clear .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  FloatingActionButton .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Home .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  MeasureBlue .androidx.compose.foundation.layout.ColumnScope  MeasurementToolbar .androidx.compose.foundation.layout.ColumnScope  MeasurementType .androidx.compose.foundation.layout.ColumnScope  MeasurementTypeButton .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Star .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  ToolMenu .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getCLIP .androidx.compose.foundation.layout.ColumnScope  getClip .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  	AROverlay +androidx.compose.foundation.layout.RowScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Build +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Clear +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  FloatingActionButton +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Home +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  MeasureBlue +androidx.compose.foundation.layout.RowScope  MeasurementType +androidx.compose.foundation.layout.RowScope  MeasurementTypeButton +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  QualityIndicator +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
getBACKGROUND +androidx.compose.foundation.layout.RowScope  
getBackground +androidx.compose.foundation.layout.RowScope  getCLIP +androidx.compose.foundation.layout.RowScope  getClip +androidx.compose.foundation.layout.RowScope  getLET +androidx.compose.foundation.layout.RowScope  getLet +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Build ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  ARCameraView &androidx.compose.material.icons.filled  	AROverlay &androidx.compose.material.icons.filled  Add &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  BottomMeasurementControls &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Build &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  CalibrationOverlay &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  ExperimentalPermissionsApi &androidx.compose.material.icons.filled  FloatingActionButton &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  
LoadingScreen &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  MeasureBlue &androidx.compose.material.icons.filled  MeasurementInstructions &androidx.compose.material.icons.filled  MeasurementToolbar &androidx.compose.material.icons.filled  MeasurementType &androidx.compose.material.icons.filled  MeasurementTypeButton &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  PermissionRequestScreen &androidx.compose.material.icons.filled  QualityIndicator &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  ToolMenu &androidx.compose.material.icons.filled  TopStatusBar &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  to &androidx.compose.material.icons.filled  ARCameraView androidx.compose.material3  	AROverlay androidx.compose.material3  Add androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  BottomMeasurementControls androidx.compose.material3  Box androidx.compose.material3  Build androidx.compose.material3  Button androidx.compose.material3  CalibrationOverlay androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  Check androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Clear androidx.compose.material3  Close androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Edit androidx.compose.material3  ExperimentalPermissionsApi androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  Home androidx.compose.material3  Icon androidx.compose.material3  Icons androidx.compose.material3  LaunchedEffect androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  
LoadingScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  MeasureBlue androidx.compose.material3  MeasurementInstructions androidx.compose.material3  MeasurementToolbar androidx.compose.material3  MeasurementType androidx.compose.material3  MeasurementTypeButton androidx.compose.material3  Modifier androidx.compose.material3  PermissionRequestScreen androidx.compose.material3  QualityIndicator androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  Spacer androidx.compose.material3  Star androidx.compose.material3  Text androidx.compose.material3  ToolMenu androidx.compose.material3  TopStatusBar androidx.compose.material3  
Typography androidx.compose.material3  android androidx.compose.material3  
background androidx.compose.material3  clip androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotEmpty androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  to androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  ARCameraView androidx.compose.runtime  	AROverlay androidx.compose.runtime  
ARRenderer androidx.compose.runtime  Add androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  BackgroundRenderer androidx.compose.runtime  BottomMeasurementControls androidx.compose.runtime  Box androidx.compose.runtime  Build androidx.compose.runtime  Button androidx.compose.runtime  CalibrationOverlay androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Check androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Clear androidx.compose.runtime  Close androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Config androidx.compose.runtime  
Coordinates2d androidx.compose.runtime  DisplayRotationHelper androidx.compose.runtime  Edit androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalPermissionsApi androidx.compose.runtime  
FloatArray androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  Frame androidx.compose.runtime  GLES20 androidx.compose.runtime  Home androidx.compose.runtime  Icon androidx.compose.runtime  Icons androidx.compose.runtime  	Immutable androidx.compose.runtime  IntArray androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  
LoadingScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  MeasureBlue androidx.compose.runtime  MeasurementInstructions androidx.compose.runtime  MeasurementToolbar androidx.compose.runtime  MeasurementType androidx.compose.runtime  MeasurementTypeButton androidx.compose.runtime  Modifier androidx.compose.runtime  MotionEvent androidx.compose.runtime  MutableState androidx.compose.runtime  PermissionRequestScreen androidx.compose.runtime  Plane androidx.compose.runtime  
PlaneRenderer androidx.compose.runtime  
PointCloud androidx.compose.runtime  PointCloudRenderer androidx.compose.runtime  Pose androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  QualityIndicator androidx.compose.runtime  RENDERMODE_CONTINUOUSLY androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Session androidx.compose.runtime  Settings androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  Star androidx.compose.runtime  State androidx.compose.runtime  Text androidx.compose.runtime  ToolMenu androidx.compose.runtime  TopStatusBar androidx.compose.runtime  
TrackingState androidx.compose.runtime  android androidx.compose.runtime  apply androidx.compose.runtime  
background androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  floatArrayOf androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  java androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  to androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Top androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  Top 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLIP androidx.compose.ui.Modifier  getClip androidx.compose.ui.Modifier  getFILLMaxSize androidx.compose.ui.Modifier  getFILLMaxWidth androidx.compose.ui.Modifier  getFillMaxSize androidx.compose.ui.Modifier  getFillMaxWidth androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getALIGN &androidx.compose.ui.Modifier.Companion  getAlign &androidx.compose.ui.Modifier.Companion  
getBACKGROUND &androidx.compose.ui.Modifier.Companion  
getBackground &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  Cyan "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  getTO "androidx.compose.ui.graphics.Color  	getTOArgb "androidx.compose.ui.graphics.Color  getTo "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  to "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Cyan ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  
MeasureApp #androidx.core.app.ComponentActivity  MeasureTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  AndroidViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  AREngine #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Float #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  Math #androidx.lifecycle.AndroidViewModel  MeasureUiState #androidx.lifecycle.AndroidViewModel  Measurement #androidx.lifecycle.AndroidViewModel  MeasurementQuality #androidx.lifecycle.AndroidViewModel  MeasurementSession #androidx.lifecycle.AndroidViewModel  MeasurementType #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  Point3D #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  System #androidx.lifecycle.AndroidViewModel  _uiState #androidx.lifecycle.AndroidViewModel  addMeasurementPoint #androidx.lifecycle.AndroidViewModel  addMeasurementToSession #androidx.lifecycle.AndroidViewModel  arEngine #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  calculateConfidence #androidx.lifecycle.AndroidViewModel  calculatePolygonArea #androidx.lifecycle.AndroidViewModel  checkCalibration #androidx.lifecycle.AndroidViewModel  clearMeasurements #androidx.lifecycle.AndroidViewModel  completeMeasurement #androidx.lifecycle.AndroidViewModel  createAreaMeasurement #androidx.lifecycle.AndroidViewModel  createLineMeasurement #androidx.lifecycle.AndroidViewModel  createMultiSegmentMeasurement #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  first #androidx.lifecycle.AndroidViewModel  generateMeasurementId #androidx.lifecycle.AndroidViewModel  generateSessionId #androidx.lifecycle.AndroidViewModel  indices #androidx.lifecycle.AndroidViewModel  initializeAR #androidx.lifecycle.AndroidViewModel  invoke #androidx.lifecycle.AndroidViewModel  
isNotEmpty #androidx.lifecycle.AndroidViewModel  kotlin #androidx.lifecycle.AndroidViewModel  launch #androidx.lifecycle.AndroidViewModel  let #androidx.lifecycle.AndroidViewModel  listOf #androidx.lifecycle.AndroidViewModel  	onCleared #androidx.lifecycle.AndroidViewModel  plus #androidx.lifecycle.AndroidViewModel  
plusAssign #androidx.lifecycle.AndroidViewModel  startCalibration #androidx.lifecycle.AndroidViewModel  startMeasurement #androidx.lifecycle.AndroidViewModel  until #androidx.lifecycle.AndroidViewModel  viewModelScope #androidx.lifecycle.AndroidViewModel  AREngine androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Math androidx.lifecycle.ViewModel  MeasureUiState androidx.lifecycle.ViewModel  Measurement androidx.lifecycle.ViewModel  MeasurementQuality androidx.lifecycle.ViewModel  MeasurementSession androidx.lifecycle.ViewModel  MeasurementType androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Point3D androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  System androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  addMeasurementPoint androidx.lifecycle.ViewModel  addMeasurementToSession androidx.lifecycle.ViewModel  arEngine androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  calculateConfidence androidx.lifecycle.ViewModel  calculatePolygonArea androidx.lifecycle.ViewModel  checkCalibration androidx.lifecycle.ViewModel  clearMeasurements androidx.lifecycle.ViewModel  completeMeasurement androidx.lifecycle.ViewModel  createAreaMeasurement androidx.lifecycle.ViewModel  createLineMeasurement androidx.lifecycle.ViewModel  createMultiSegmentMeasurement androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  first androidx.lifecycle.ViewModel  generateMeasurementId androidx.lifecycle.ViewModel  generateSessionId androidx.lifecycle.ViewModel  indices androidx.lifecycle.ViewModel  initializeAR androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  
isNotEmpty androidx.lifecycle.ViewModel  kotlin androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  let androidx.lifecycle.ViewModel  listOf androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  plus androidx.lifecycle.ViewModel  
plusAssign androidx.lifecycle.ViewModel  startCalibration androidx.lifecycle.ViewModel  startMeasurement androidx.lifecycle.ViewModel  until androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  ExperimentalPermissionsApi "com.google.accompanist.permissions  PermissionState "com.google.accompanist.permissions  	isGranted "com.google.accompanist.permissions  rememberPermissionState "com.google.accompanist.permissions  launchPermissionRequest 2com.google.accompanist.permissions.PermissionState  status 2com.google.accompanist.permissions.PermissionState  getISGranted 3com.google.accompanist.permissions.PermissionStatus  getIsGranted 3com.google.accompanist.permissions.PermissionStatus  	isGranted 3com.google.accompanist.permissions.PermissionStatus  
ARRenderer com.google.ar.core  ARSessionState com.google.ar.core  	ArCoreApk com.google.ar.core  BackgroundRenderer com.google.ar.core  Camera com.google.ar.core  CameraNotAvailableException com.google.ar.core  
Composable com.google.ar.core  Config com.google.ar.core  Context com.google.ar.core  
Coordinates2d com.google.ar.core  DisplayRotationHelper com.google.ar.core  	Exception com.google.ar.core  
FloatArray com.google.ar.core  Frame com.google.ar.core  GLES20 com.google.ar.core  	HitResult com.google.ar.core  IntArray com.google.ar.core  Math com.google.ar.core  Measurement com.google.ar.core  MeasurementQuality com.google.ar.core  MeasurementType com.google.ar.core  MotionEvent com.google.ar.core  MutableStateFlow com.google.ar.core  Plane com.google.ar.core  
PlaneRenderer com.google.ar.core  Point3D com.google.ar.core  
PointCloud com.google.ar.core  PointCloudRenderer com.google.ar.core  Pose com.google.ar.core  RENDERMODE_CONTINUOUSLY com.google.ar.core  Sensor com.google.ar.core  
SensorManager com.google.ar.core  Session com.google.ar.core  System com.google.ar.core  
TrackingState com.google.ar.core  apply com.google.ar.core  asStateFlow com.google.ar.core  	emptyList com.google.ar.core  floatArrayOf com.google.ar.core  java com.google.ar.core  let com.google.ar.core  listOf com.google.ar.core  Availability com.google.ar.core.ArCoreApk  checkAvailability com.google.ar.core.ArCoreApk  getInstance com.google.ar.core.ArCoreApk  SUPPORTED_APK_TOO_OLD )com.google.ar.core.ArCoreApk.Availability  SUPPORTED_INSTALLED )com.google.ar.core.ArCoreApk.Availability  SUPPORTED_NOT_INSTALLED )com.google.ar.core.ArCoreApk.Availability  displayOrientedPose com.google.ar.core.Camera  getDISPLAYOrientedPose com.google.ar.core.Camera  getDisplayOrientedPose com.google.ar.core.Camera  getProjectionMatrix com.google.ar.core.Camera  getTRACKINGState com.google.ar.core.Camera  getTrackingState com.google.ar.core.Camera  
getViewMatrix com.google.ar.core.Camera  setDisplayOrientedPose com.google.ar.core.Camera  setTrackingState com.google.ar.core.Camera  
trackingState com.google.ar.core.Camera  Config com.google.ar.core.Config  	DepthMode com.google.ar.core.Config  InstantPlacementMode com.google.ar.core.Config  LightEstimationMode com.google.ar.core.Config  PlaneFindingMode com.google.ar.core.Config  apply com.google.ar.core.Config  	depthMode com.google.ar.core.Config  getAPPLY com.google.ar.core.Config  getApply com.google.ar.core.Config  getDEPTHMode com.google.ar.core.Config  getDepthMode com.google.ar.core.Config  getINSTANTPlacementMode com.google.ar.core.Config  getInstantPlacementMode com.google.ar.core.Config  getLIGHTEstimationMode com.google.ar.core.Config  getLightEstimationMode com.google.ar.core.Config  getPLANEFindingMode com.google.ar.core.Config  getPlaneFindingMode com.google.ar.core.Config  instantPlacementMode com.google.ar.core.Config  lightEstimationMode com.google.ar.core.Config  planeFindingMode com.google.ar.core.Config  setDepthMode com.google.ar.core.Config  setInstantPlacementMode com.google.ar.core.Config  setLightEstimationMode com.google.ar.core.Config  setPlaneFindingMode com.google.ar.core.Config  	AUTOMATIC #com.google.ar.core.Config.DepthMode  
LOCAL_Y_UP .com.google.ar.core.Config.InstantPlacementMode  ENVIRONMENTAL_HDR -com.google.ar.core.Config.LightEstimationMode  HORIZONTAL_AND_VERTICAL *com.google.ar.core.Config.PlaneFindingMode  $OPENGL_NORMALIZED_DEVICE_COORDINATES  com.google.ar.core.Coordinates2d  TEXTURE_NORMALIZED  com.google.ar.core.Coordinates2d  acquirePointCloud com.google.ar.core.Frame  camera com.google.ar.core.Frame  	getCAMERA com.google.ar.core.Frame  	getCamera com.google.ar.core.Frame  hasDisplayGeometryChanged com.google.ar.core.Frame  hitTest com.google.ar.core.Frame  	setCamera com.google.ar.core.Frame  transformCoordinates2d com.google.ar.core.Frame  
getHITPose com.google.ar.core.HitResult  
getHitPose com.google.ar.core.HitResult  hitPose com.google.ar.core.HitResult  
setHitPose com.google.ar.core.HitResult  equals com.google.ar.core.Plane  
getSUBSUMEDBy com.google.ar.core.Plane  
getSubsumedBy com.google.ar.core.Plane  getTRACKINGState com.google.ar.core.Plane  getTrackingState com.google.ar.core.Plane  
setSubsumedBy com.google.ar.core.Plane  setTrackingState com.google.ar.core.Plane  
subsumedBy com.google.ar.core.Plane  
trackingState com.google.ar.core.Plane  release com.google.ar.core.PointCloud  tx com.google.ar.core.Pose  ty com.google.ar.core.Pose  tz com.google.ar.core.Pose  Config com.google.ar.core.Session  apply com.google.ar.core.Session  close com.google.ar.core.Session  	configure com.google.ar.core.Session  equals com.google.ar.core.Session  getAPPLY com.google.ar.core.Session  getAllTrackables com.google.ar.core.Session  getApply com.google.ar.core.Session  getLET com.google.ar.core.Session  getLet com.google.ar.core.Session  isDepthModeSupported com.google.ar.core.Session  let com.google.ar.core.Session  pause com.google.ar.core.Session  resume com.google.ar.core.Session  setCameraTextureName com.google.ar.core.Session  setDisplayGeometry com.google.ar.core.Session  update com.google.ar.core.Session  TRACKING  com.google.ar.core.TrackingState  equals  com.google.ar.core.TrackingState  ARSessionState com.google.ar.core.exceptions  	ArCoreApk com.google.ar.core.exceptions  CameraNotAvailableException com.google.ar.core.exceptions  Config com.google.ar.core.exceptions  Context com.google.ar.core.exceptions  	Exception com.google.ar.core.exceptions  
FloatArray com.google.ar.core.exceptions  Frame com.google.ar.core.exceptions  	HitResult com.google.ar.core.exceptions  Math com.google.ar.core.exceptions  Measurement com.google.ar.core.exceptions  MeasurementQuality com.google.ar.core.exceptions  MeasurementType com.google.ar.core.exceptions  MutableStateFlow com.google.ar.core.exceptions  Point3D com.google.ar.core.exceptions  Sensor com.google.ar.core.exceptions  
SensorManager com.google.ar.core.exceptions  Session com.google.ar.core.exceptions  System com.google.ar.core.exceptions  
TrackingState com.google.ar.core.exceptions  apply com.google.ar.core.exceptions  asStateFlow com.google.ar.core.exceptions  	emptyList com.google.ar.core.exceptions  let com.google.ar.core.exceptions  listOf com.google.ar.core.exceptions  MainActivity com.measure.ar  
MeasureApp com.measure.ar  MeasureAppPreview com.measure.ar  MeasureTheme com.measure.ar  Modifier com.measure.ar  Scaffold com.measure.ar  fillMaxSize com.measure.ar  padding com.measure.ar  
setContent com.measure.ar  Bundle com.measure.ar.MainActivity  
MeasureApp com.measure.ar.MainActivity  MeasureTheme com.measure.ar.MainActivity  Modifier com.measure.ar.MainActivity  Scaffold com.measure.ar.MainActivity  fillMaxSize com.measure.ar.MainActivity  getFILLMaxSize com.measure.ar.MainActivity  getFillMaxSize com.measure.ar.MainActivity  
getPADDING com.measure.ar.MainActivity  
getPadding com.measure.ar.MainActivity  
getSETContent com.measure.ar.MainActivity  
getSetContent com.measure.ar.MainActivity  padding com.measure.ar.MainActivity  
setContent com.measure.ar.MainActivity  AREngine com.measure.ar.ar  ARSessionState com.measure.ar.ar  	ArCoreApk com.measure.ar.ar  Boolean com.measure.ar.ar  CameraNotAvailableException com.measure.ar.ar  Config com.measure.ar.ar  Context com.measure.ar.ar  	Exception com.measure.ar.ar  Float com.measure.ar.ar  
FloatArray com.measure.ar.ar  Frame com.measure.ar.ar  	HitResult com.measure.ar.ar  Int com.measure.ar.ar  List com.measure.ar.ar  Math com.measure.ar.ar  Measurement com.measure.ar.ar  MeasurementQuality com.measure.ar.ar  MeasurementType com.measure.ar.ar  MutableStateFlow com.measure.ar.ar  Point3D com.measure.ar.ar  Sensor com.measure.ar.ar  
SensorManager com.measure.ar.ar  Session com.measure.ar.ar  String com.measure.ar.ar  System com.measure.ar.ar  
TrackingState com.measure.ar.ar  apply com.measure.ar.ar  asStateFlow com.measure.ar.ar  	emptyList com.measure.ar.ar  let com.measure.ar.ar  listOf com.measure.ar.ar  ARSessionState com.measure.ar.ar.AREngine  	ArCoreApk com.measure.ar.ar.AREngine  Boolean com.measure.ar.ar.AREngine  CameraNotAvailableException com.measure.ar.ar.AREngine  Config com.measure.ar.ar.AREngine  Context com.measure.ar.ar.AREngine  	Exception com.measure.ar.ar.AREngine  Float com.measure.ar.ar.AREngine  
FloatArray com.measure.ar.ar.AREngine  Frame com.measure.ar.ar.AREngine  	HitResult com.measure.ar.ar.AREngine  Int com.measure.ar.ar.AREngine  List com.measure.ar.ar.AREngine  Math com.measure.ar.ar.AREngine  Measurement com.measure.ar.ar.AREngine  MeasurementQuality com.measure.ar.ar.AREngine  MeasurementType com.measure.ar.ar.AREngine  MutableStateFlow com.measure.ar.ar.AREngine  Point3D com.measure.ar.ar.AREngine  Sensor com.measure.ar.ar.AREngine  SensorEvent com.measure.ar.ar.AREngine  
SensorManager com.measure.ar.ar.AREngine  Session com.measure.ar.ar.AREngine  	StateFlow com.measure.ar.ar.AREngine  String com.measure.ar.ar.AREngine  System com.measure.ar.ar.AREngine  
TrackingState com.measure.ar.ar.AREngine  
_sessionState com.measure.ar.ar.AREngine  _trackingQuality com.measure.ar.ar.AREngine  
accelerometer com.measure.ar.ar.AREngine  accelerometerData com.measure.ar.ar.AREngine  apply com.measure.ar.ar.AREngine  	arSession com.measure.ar.ar.AREngine  asStateFlow com.measure.ar.ar.AREngine  calculateConfidence com.measure.ar.ar.AREngine  calculateMeasurementQuality com.measure.ar.ar.AREngine  calibrationSamples com.measure.ar.ar.AREngine  cleanup com.measure.ar.ar.AREngine  context com.measure.ar.ar.AREngine  
createSession com.measure.ar.ar.AREngine  	emptyList com.measure.ar.ar.AREngine  generateMeasurementId com.measure.ar.ar.AREngine  getAPPLY com.measure.ar.ar.AREngine  getASStateFlow com.measure.ar.ar.AREngine  getApply com.measure.ar.ar.AREngine  getAsStateFlow com.measure.ar.ar.AREngine  getEMPTYList com.measure.ar.ar.AREngine  getEmptyList com.measure.ar.ar.AREngine  getLET com.measure.ar.ar.AREngine  	getLISTOf com.measure.ar.ar.AREngine  getLet com.measure.ar.ar.AREngine  	getListOf com.measure.ar.ar.AREngine  	gyroscope com.measure.ar.ar.AREngine  
gyroscopeData com.measure.ar.ar.AREngine  hitTest com.measure.ar.ar.AREngine  
initialize com.measure.ar.ar.AREngine  isCalibrated com.measure.ar.ar.AREngine  isCalibrationComplete com.measure.ar.ar.AREngine  let com.measure.ar.ar.AREngine  listOf com.measure.ar.ar.AREngine  magnetometer com.measure.ar.ar.AREngine  magnetometerData com.measure.ar.ar.AREngine  orientationAngles com.measure.ar.ar.AREngine  pause com.measure.ar.ar.AREngine  requiredCalibrationSamples com.measure.ar.ar.AREngine  resume com.measure.ar.ar.AREngine  rotationMatrix com.measure.ar.ar.AREngine  
sensorManager com.measure.ar.ar.AREngine  sessionState com.measure.ar.ar.AREngine  startCalibration com.measure.ar.ar.AREngine  startSensorListening com.measure.ar.ar.AREngine  trackingQuality com.measure.ar.ar.AREngine  updateCalibration com.measure.ar.ar.AREngine  
updateSession com.measure.ar.ar.AREngine  AREngine com.measure.ar.data  ARSessionState com.measure.ar.data  ARTrackingState com.measure.ar.data  	ArCoreApk com.measure.ar.data  Boolean com.measure.ar.data  CameraIntrinsics com.measure.ar.data  CameraNotAvailableException com.measure.ar.data  Config com.measure.ar.data  Context com.measure.ar.data  	Exception com.measure.ar.data  Float com.measure.ar.data  
FloatArray com.measure.ar.data  Frame com.measure.ar.data  	HitResult com.measure.ar.data  Int com.measure.ar.data  List com.measure.ar.data  Long com.measure.ar.data  Math com.measure.ar.data  MeasureUiState com.measure.ar.data  Measurement com.measure.ar.data  MeasurementQuality com.measure.ar.data  MeasurementSession com.measure.ar.data  MeasurementType com.measure.ar.data  MutableStateFlow com.measure.ar.data  Point3D com.measure.ar.data  Sensor com.measure.ar.data  
SensorManager com.measure.ar.data  Session com.measure.ar.data  	StateFlow com.measure.ar.data  String com.measure.ar.data  System com.measure.ar.data  
TrackingState com.measure.ar.data  _uiState com.measure.ar.data  apply com.measure.ar.data  arEngine com.measure.ar.data  asStateFlow com.measure.ar.data  	emptyList com.measure.ar.data  first com.measure.ar.data  floatArrayOf com.measure.ar.data  format com.measure.ar.data  indices com.measure.ar.data  
isNotEmpty com.measure.ar.data  kotlin com.measure.ar.data  launch com.measure.ar.data  let com.measure.ar.data  listOf com.measure.ar.data  plus com.measure.ar.data  
plusAssign com.measure.ar.data  sqrt com.measure.ar.data  until com.measure.ar.data  viewModelScope com.measure.ar.data  ARTrackingState "com.measure.ar.data.ARSessionState  Boolean "com.measure.ar.data.ARSessionState  CameraIntrinsics "com.measure.ar.data.ARSessionState  Float "com.measure.ar.data.ARSessionState  copy "com.measure.ar.data.ARSessionState  lightEstimation "com.measure.ar.data.ARSessionState  NOT_TRACKING #com.measure.ar.data.ARTrackingState  Float $com.measure.ar.data.CameraIntrinsics  Int $com.measure.ar.data.CameraIntrinsics  Float com.measure.ar.data.Measurement  List com.measure.ar.data.Measurement  Long com.measure.ar.data.Measurement  MeasurementQuality com.measure.ar.data.Measurement  MeasurementType com.measure.ar.data.Measurement  Point3D com.measure.ar.data.Measurement  String com.measure.ar.data.Measurement  System com.measure.ar.data.Measurement  
confidence com.measure.ar.data.Measurement  confidenceRange com.measure.ar.data.Measurement  displayValue com.measure.ar.data.Measurement  format com.measure.ar.data.Measurement  	getFORMAT com.measure.ar.data.Measurement  	getFormat com.measure.ar.data.Measurement  getLET com.measure.ar.data.Measurement  getLet com.measure.ar.data.Measurement  let com.measure.ar.data.Measurement  unit com.measure.ar.data.Measurement  value com.measure.ar.data.Measurement  	EXCELLENT &com.measure.ar.data.MeasurementQuality  FAIR &com.measure.ar.data.MeasurementQuality  GOOD &com.measure.ar.data.MeasurementQuality  POOR &com.measure.ar.data.MeasurementQuality  Float &com.measure.ar.data.MeasurementSession  List &com.measure.ar.data.MeasurementSession  Long &com.measure.ar.data.MeasurementSession  Measurement &com.measure.ar.data.MeasurementSession  MeasurementQuality &com.measure.ar.data.MeasurementSession  String &com.measure.ar.data.MeasurementSession  System &com.measure.ar.data.MeasurementSession  copy &com.measure.ar.data.MeasurementSession  	emptyList &com.measure.ar.data.MeasurementSession  measurements &com.measure.ar.data.MeasurementSession  AREA #com.measure.ar.data.MeasurementType  LEVEL #com.measure.ar.data.MeasurementType  
MULTI_SEGMENT #com.measure.ar.data.MeasurementType  SINGLE_LINE #com.measure.ar.data.MeasurementType  VOLUME #com.measure.ar.data.MeasurementType  Float com.measure.ar.data.Point3D  
FloatArray com.measure.ar.data.Point3D  Point3D com.measure.ar.data.Point3D  
distanceTo com.measure.ar.data.Point3D  floatArrayOf com.measure.ar.data.Point3D  getFLOATArrayOf com.measure.ar.data.Point3D  getFloatArrayOf com.measure.ar.data.Point3D  getSQRT com.measure.ar.data.Point3D  getSqrt com.measure.ar.data.Point3D  sqrt com.measure.ar.data.Point3D  x com.measure.ar.data.Point3D  y com.measure.ar.data.Point3D  z com.measure.ar.data.Point3D  ARCameraView com.measure.ar.ui.components  ARGLSurfaceView com.measure.ar.ui.components  
ARRenderer com.measure.ar.ui.components  Add com.measure.ar.ui.components  	Alignment com.measure.ar.ui.components  Arrangement com.measure.ar.ui.components  BackgroundRenderer com.measure.ar.ui.components  Boolean com.measure.ar.ui.components  Box com.measure.ar.ui.components  Build com.measure.ar.ui.components  CalibrationOverlay com.measure.ar.ui.components  Card com.measure.ar.ui.components  CardDefaults com.measure.ar.ui.components  CircleShape com.measure.ar.ui.components  Close com.measure.ar.ui.components  
Collection com.measure.ar.ui.components  Color com.measure.ar.ui.components  Column com.measure.ar.ui.components  
Composable com.measure.ar.ui.components  Config com.measure.ar.ui.components  
Coordinates2d com.measure.ar.ui.components  DisplayRotationHelper com.measure.ar.ui.components  Edit com.measure.ar.ui.components  	Exception com.measure.ar.ui.components  Float com.measure.ar.ui.components  
FloatArray com.measure.ar.ui.components  FloatingActionButton com.measure.ar.ui.components  
FontWeight com.measure.ar.ui.components  Frame com.measure.ar.ui.components  GLES20 com.measure.ar.ui.components  Home com.measure.ar.ui.components  Icon com.measure.ar.ui.components  Icons com.measure.ar.ui.components  Int com.measure.ar.ui.components  IntArray com.measure.ar.ui.components  LaunchedEffect com.measure.ar.ui.components  LinearProgressIndicator com.measure.ar.ui.components  
MaterialTheme com.measure.ar.ui.components  MeasureBlue com.measure.ar.ui.components  MeasurementToolbar com.measure.ar.ui.components  MeasurementType com.measure.ar.ui.components  MeasurementTypeButton com.measure.ar.ui.components  Modifier com.measure.ar.ui.components  MotionEvent com.measure.ar.ui.components  Plane com.measure.ar.ui.components  
PlaneRenderer com.measure.ar.ui.components  
PointCloud com.measure.ar.ui.components  PointCloudRenderer com.measure.ar.ui.components  Pose com.measure.ar.ui.components  QualityIndicator com.measure.ar.ui.components  RENDERMODE_CONTINUOUSLY com.measure.ar.ui.components  RoundedCornerShape com.measure.ar.ui.components  Row com.measure.ar.ui.components  Session com.measure.ar.ui.components  Settings com.measure.ar.ui.components  Spacer com.measure.ar.ui.components  Star com.measure.ar.ui.components  String com.measure.ar.ui.components  Text com.measure.ar.ui.components  ToolMenu com.measure.ar.ui.components  
TrackingState com.measure.ar.ui.components  Unit com.measure.ar.ui.components  apply com.measure.ar.ui.components  
background com.measure.ar.ui.components  clip com.measure.ar.ui.components  com com.measure.ar.ui.components  fillMaxWidth com.measure.ar.ui.components  floatArrayOf com.measure.ar.ui.components  getValue com.measure.ar.ui.components  height com.measure.ar.ui.components  java com.measure.ar.ui.components  kotlinx com.measure.ar.ui.components  let com.measure.ar.ui.components  mutableStateOf com.measure.ar.ui.components  padding com.measure.ar.ui.components  provideDelegate com.measure.ar.ui.components  remember com.measure.ar.ui.components  setValue com.measure.ar.ui.components  size com.measure.ar.ui.components  to com.measure.ar.ui.components  
ARRenderer ,com.measure.ar.ui.components.ARGLSurfaceView  Boolean ,com.measure.ar.ui.components.ARGLSurfaceView  Context ,com.measure.ar.ui.components.ARGLSurfaceView  Float ,com.measure.ar.ui.components.ARGLSurfaceView  MotionEvent ,com.measure.ar.ui.components.ARGLSurfaceView  RENDERMODE_CONTINUOUSLY ,com.measure.ar.ui.components.ARGLSurfaceView  Unit ,com.measure.ar.ui.components.ARGLSurfaceView  getPreserveEGLContextOnPause ,com.measure.ar.ui.components.ARGLSurfaceView  
getRENDERMode ,com.measure.ar.ui.components.ARGLSurfaceView  
getRenderMode ,com.measure.ar.ui.components.ARGLSurfaceView  invoke ,com.measure.ar.ui.components.ARGLSurfaceView  onTap ,com.measure.ar.ui.components.ARGLSurfaceView  preserveEGLContextOnPause ,com.measure.ar.ui.components.ARGLSurfaceView  
renderMode ,com.measure.ar.ui.components.ARGLSurfaceView  renderer ,com.measure.ar.ui.components.ARGLSurfaceView  setEGLConfigChooser ,com.measure.ar.ui.components.ARGLSurfaceView  setEGLContextClientVersion ,com.measure.ar.ui.components.ARGLSurfaceView  setPreserveEGLContextOnPause ,com.measure.ar.ui.components.ARGLSurfaceView  
setRenderMode ,com.measure.ar.ui.components.ARGLSurfaceView  setRenderer ,com.measure.ar.ui.components.ARGLSurfaceView  setWillNotDraw ,com.measure.ar.ui.components.ARGLSurfaceView  BackgroundRenderer 'com.measure.ar.ui.components.ARRenderer  CameraNotAvailableException 'com.measure.ar.ui.components.ARRenderer  Config 'com.measure.ar.ui.components.ARRenderer  Context 'com.measure.ar.ui.components.ARRenderer  DisplayRotationHelper 'com.measure.ar.ui.components.ARRenderer  	EGLConfig 'com.measure.ar.ui.components.ARRenderer  	Exception 'com.measure.ar.ui.components.ARRenderer  
FloatArray 'com.measure.ar.ui.components.ARRenderer  GL10 'com.measure.ar.ui.components.ARRenderer  GLES20 'com.measure.ar.ui.components.ARRenderer  Int 'com.measure.ar.ui.components.ARRenderer  Plane 'com.measure.ar.ui.components.ARRenderer  
PlaneRenderer 'com.measure.ar.ui.components.ARRenderer  PointCloudRenderer 'com.measure.ar.ui.components.ARRenderer  Session 'com.measure.ar.ui.components.ARRenderer  
TrackingState 'com.measure.ar.ui.components.ARRenderer  apply 'com.measure.ar.ui.components.ARRenderer  backgroundRenderer 'com.measure.ar.ui.components.ARRenderer  context 'com.measure.ar.ui.components.ARRenderer  displayRotationHelper 'com.measure.ar.ui.components.ARRenderer  getAPPLY 'com.measure.ar.ui.components.ARRenderer  getApply 'com.measure.ar.ui.components.ARRenderer  getLET 'com.measure.ar.ui.components.ARRenderer  getLet 'com.measure.ar.ui.components.ARRenderer  java 'com.measure.ar.ui.components.ARRenderer  let 'com.measure.ar.ui.components.ARRenderer  onPause 'com.measure.ar.ui.components.ARRenderer  onResume 'com.measure.ar.ui.components.ARRenderer  
planeRenderer 'com.measure.ar.ui.components.ARRenderer  pointCloudRenderer 'com.measure.ar.ui.components.ARRenderer  session 'com.measure.ar.ui.components.ARRenderer  Context /com.measure.ar.ui.components.BackgroundRenderer  
Coordinates2d /com.measure.ar.ui.components.BackgroundRenderer  
FloatArray /com.measure.ar.ui.components.BackgroundRenderer  Frame /com.measure.ar.ui.components.BackgroundRenderer  GLES20 /com.measure.ar.ui.components.BackgroundRenderer  IntArray /com.measure.ar.ui.components.BackgroundRenderer  createOnGlThread /com.measure.ar.ui.components.BackgroundRenderer  draw /com.measure.ar.ui.components.BackgroundRenderer  floatArrayOf /com.measure.ar.ui.components.BackgroundRenderer  getFLOATArrayOf /com.measure.ar.ui.components.BackgroundRenderer  getFloatArrayOf /com.measure.ar.ui.components.BackgroundRenderer  	textureId /com.measure.ar.ui.components.BackgroundRenderer  Context 2com.measure.ar.ui.components.DisplayRotationHelper  Int 2com.measure.ar.ui.components.DisplayRotationHelper  Session 2com.measure.ar.ui.components.DisplayRotationHelper  context 2com.measure.ar.ui.components.DisplayRotationHelper  onSurfaceChanged 2com.measure.ar.ui.components.DisplayRotationHelper  updateSessionIfNeeded 2com.measure.ar.ui.components.DisplayRotationHelper  viewportChanged 2com.measure.ar.ui.components.DisplayRotationHelper  viewportHeight 2com.measure.ar.ui.components.DisplayRotationHelper  
viewportWidth 2com.measure.ar.ui.components.DisplayRotationHelper  
Collection *com.measure.ar.ui.components.PlaneRenderer  Context *com.measure.ar.ui.components.PlaneRenderer  
FloatArray *com.measure.ar.ui.components.PlaneRenderer  Plane *com.measure.ar.ui.components.PlaneRenderer  Pose *com.measure.ar.ui.components.PlaneRenderer  String *com.measure.ar.ui.components.PlaneRenderer  
TrackingState *com.measure.ar.ui.components.PlaneRenderer  createOnGlThread *com.measure.ar.ui.components.PlaneRenderer  
drawPlanes *com.measure.ar.ui.components.PlaneRenderer  Context /com.measure.ar.ui.components.PointCloudRenderer  
FloatArray /com.measure.ar.ui.components.PointCloudRenderer  
PointCloud /com.measure.ar.ui.components.PointCloudRenderer  createOnGlThread /com.measure.ar.ui.components.PointCloudRenderer  draw /com.measure.ar.ui.components.PointCloudRenderer  update /com.measure.ar.ui.components.PointCloudRenderer  ARCameraView com.measure.ar.ui.screens  	AROverlay com.measure.ar.ui.screens  	Alignment com.measure.ar.ui.screens  Arrangement com.measure.ar.ui.screens  BottomMeasurementControls com.measure.ar.ui.screens  Box com.measure.ar.ui.screens  Button com.measure.ar.ui.screens  CalibrationOverlay com.measure.ar.ui.screens  Card com.measure.ar.ui.screens  CardDefaults com.measure.ar.ui.screens  Check com.measure.ar.ui.screens  CircularProgressIndicator com.measure.ar.ui.screens  Clear com.measure.ar.ui.screens  Color com.measure.ar.ui.screens  Column com.measure.ar.ui.screens  
Composable com.measure.ar.ui.screens  ExperimentalPermissionsApi com.measure.ar.ui.screens  FloatingActionButton com.measure.ar.ui.screens  
FontWeight com.measure.ar.ui.screens  Icon com.measure.ar.ui.screens  Icons com.measure.ar.ui.screens  Int com.measure.ar.ui.screens  LaunchedEffect com.measure.ar.ui.screens  
LoadingScreen com.measure.ar.ui.screens  
MaterialTheme com.measure.ar.ui.screens  MeasureBlue com.measure.ar.ui.screens  
MeasureScreen com.measure.ar.ui.screens  MeasurementInstructions com.measure.ar.ui.screens  MeasurementToolbar com.measure.ar.ui.screens  MeasurementType com.measure.ar.ui.screens  Modifier com.measure.ar.ui.screens  OptIn com.measure.ar.ui.screens  PermissionRequestScreen com.measure.ar.ui.screens  QualityIndicator com.measure.ar.ui.screens  RoundedCornerShape com.measure.ar.ui.screens  Row com.measure.ar.ui.screens  Settings com.measure.ar.ui.screens  Spacer com.measure.ar.ui.screens  Text com.measure.ar.ui.screens  TopStatusBar com.measure.ar.ui.screens  Unit com.measure.ar.ui.screens  android com.measure.ar.ui.screens  
background com.measure.ar.ui.screens  com com.measure.ar.ui.screens  fillMaxSize com.measure.ar.ui.screens  fillMaxWidth com.measure.ar.ui.screens  getValue com.measure.ar.ui.screens  height com.measure.ar.ui.screens  
isNotEmpty com.measure.ar.ui.screens  let com.measure.ar.ui.screens  padding com.measure.ar.ui.screens  provideDelegate com.measure.ar.ui.screens  size com.measure.ar.ui.screens  
ARMeasureLine com.measure.ar.ui.theme  	AROverlay com.measure.ar.ui.theme  	ARReticle com.measure.ar.ui.theme  ARSnapIndicator com.measure.ar.ui.theme  Boolean com.measure.ar.ui.theme  Build com.measure.ar.ui.theme  DarkColorScheme com.measure.ar.ui.theme  LightColorScheme com.measure.ar.ui.theme  MeasureBlue com.measure.ar.ui.theme  MeasureGreen com.measure.ar.ui.theme  
MeasureOrange com.measure.ar.ui.theme  
MeasureRed com.measure.ar.ui.theme  MeasureTheme com.measure.ar.ui.theme  Pink40 com.measure.ar.ui.theme  Pink80 com.measure.ar.ui.theme  Purple40 com.measure.ar.ui.theme  Purple80 com.measure.ar.ui.theme  PurpleGrey40 com.measure.ar.ui.theme  PurpleGrey80 com.measure.ar.ui.theme  
Typography com.measure.ar.ui.theme  Unit com.measure.ar.ui.theme  WindowCompat com.measure.ar.ui.theme  Boolean com.measure.ar.utils  Float com.measure.ar.utils  Int com.measure.ar.utils  List com.measure.ar.utils  	MathUtils com.measure.ar.utils  MeasurementType com.measure.ar.utils  PI com.measure.ar.utils  Point3D com.measure.ar.utils  String com.measure.ar.utils  
UnitConverter com.measure.ar.utils  
UnitSystem com.measure.ar.utils  abs com.measure.ar.utils  acos com.measure.ar.utils  coerceIn com.measure.ar.utils  first com.measure.ar.utils  format com.measure.ar.utils  indices com.measure.ar.utils  last com.measure.ar.utils  listOf com.measure.ar.utils  	lowercase com.measure.ar.utils  maxOf com.measure.ar.utils  minOf com.measure.ar.utils  
mutableListOf com.measure.ar.utils  
plusAssign com.measure.ar.utils  sqrt com.measure.ar.utils  sumOf com.measure.ar.utils  until com.measure.ar.utils  Boolean com.measure.ar.utils.MathUtils  Float com.measure.ar.utils.MathUtils  Int com.measure.ar.utils.MathUtils  List com.measure.ar.utils.MathUtils  PI com.measure.ar.utils.MathUtils  Point3D com.measure.ar.utils.MathUtils  abs com.measure.ar.utils.MathUtils  acos com.measure.ar.utils.MathUtils  coerceIn com.measure.ar.utils.MathUtils  
distance3D com.measure.ar.utils.MathUtils  first com.measure.ar.utils.MathUtils  getABS com.measure.ar.utils.MathUtils  getACOS com.measure.ar.utils.MathUtils  getAbs com.measure.ar.utils.MathUtils  getAcos com.measure.ar.utils.MathUtils  getCOERCEIn com.measure.ar.utils.MathUtils  getCoerceIn com.measure.ar.utils.MathUtils  getFIRST com.measure.ar.utils.MathUtils  getFirst com.measure.ar.utils.MathUtils  getLAST com.measure.ar.utils.MathUtils  getLast com.measure.ar.utils.MathUtils  getMAXOf com.measure.ar.utils.MathUtils  getMINOf com.measure.ar.utils.MathUtils  getMUTABLEListOf com.measure.ar.utils.MathUtils  getMaxOf com.measure.ar.utils.MathUtils  getMinOf com.measure.ar.utils.MathUtils  getMutableListOf com.measure.ar.utils.MathUtils  
getPLUSAssign com.measure.ar.utils.MathUtils  
getPlusAssign com.measure.ar.utils.MathUtils  getSQRT com.measure.ar.utils.MathUtils  getSUMOf com.measure.ar.utils.MathUtils  getSqrt com.measure.ar.utils.MathUtils  getSumOf com.measure.ar.utils.MathUtils  getUNTIL com.measure.ar.utils.MathUtils  getUntil com.measure.ar.utils.MathUtils  indices com.measure.ar.utils.MathUtils  last com.measure.ar.utils.MathUtils  maxOf com.measure.ar.utils.MathUtils  minOf com.measure.ar.utils.MathUtils  
mutableListOf com.measure.ar.utils.MathUtils  
plusAssign com.measure.ar.utils.MathUtils  sqrt com.measure.ar.utils.MathUtils  sumOf com.measure.ar.utils.MathUtils  until com.measure.ar.utils.MathUtils  AREA $com.measure.ar.utils.MeasurementType  LENGTH $com.measure.ar.utils.MeasurementType  VOLUME $com.measure.ar.utils.MeasurementType  	CM2_TO_M2 "com.measure.ar.utils.UnitConverter  	CM3_TO_M3 "com.measure.ar.utils.UnitConverter  CM_TO_M "com.measure.ar.utils.UnitConverter  	FT2_TO_M2 "com.measure.ar.utils.UnitConverter  	FT3_TO_M3 "com.measure.ar.utils.UnitConverter  FT_TO_M "com.measure.ar.utils.UnitConverter  Float "com.measure.ar.utils.UnitConverter  	IN2_TO_M2 "com.measure.ar.utils.UnitConverter  	IN3_TO_M3 "com.measure.ar.utils.UnitConverter  IN_TO_M "com.measure.ar.utils.UnitConverter  L_TO_M3 "com.measure.ar.utils.UnitConverter  List "com.measure.ar.utils.UnitConverter  MM_TO_M "com.measure.ar.utils.UnitConverter  MeasurementType "com.measure.ar.utils.UnitConverter  String "com.measure.ar.utils.UnitConverter  
UnitSystem "com.measure.ar.utils.UnitConverter  YD_TO_M "com.measure.ar.utils.UnitConverter  format "com.measure.ar.utils.UnitConverter  	getFORMAT "com.measure.ar.utils.UnitConverter  	getFormat "com.measure.ar.utils.UnitConverter  	getLISTOf "com.measure.ar.utils.UnitConverter  getLOWERCASE "com.measure.ar.utils.UnitConverter  	getListOf "com.measure.ar.utils.UnitConverter  getLowercase "com.measure.ar.utils.UnitConverter  getOptimalAreaUnit "com.measure.ar.utils.UnitConverter  getOptimalLengthUnit "com.measure.ar.utils.UnitConverter  getOptimalVolumeUnit "com.measure.ar.utils.UnitConverter  listOf "com.measure.ar.utils.UnitConverter  	lowercase "com.measure.ar.utils.UnitConverter  IMPERIAL com.measure.ar.utils.UnitSystem  METRIC com.measure.ar.utils.UnitSystem  AREngine com.measure.ar.viewmodel  Boolean com.measure.ar.viewmodel  Float com.measure.ar.viewmodel  List com.measure.ar.viewmodel  Math com.measure.ar.viewmodel  MeasureUiState com.measure.ar.viewmodel  MeasureViewModel com.measure.ar.viewmodel  Measurement com.measure.ar.viewmodel  MeasurementQuality com.measure.ar.viewmodel  MeasurementSession com.measure.ar.viewmodel  MeasurementType com.measure.ar.viewmodel  MutableStateFlow com.measure.ar.viewmodel  Point3D com.measure.ar.viewmodel  	StateFlow com.measure.ar.viewmodel  String com.measure.ar.viewmodel  System com.measure.ar.viewmodel  _uiState com.measure.ar.viewmodel  arEngine com.measure.ar.viewmodel  asStateFlow com.measure.ar.viewmodel  	emptyList com.measure.ar.viewmodel  first com.measure.ar.viewmodel  indices com.measure.ar.viewmodel  
isNotEmpty com.measure.ar.viewmodel  kotlin com.measure.ar.viewmodel  launch com.measure.ar.viewmodel  let com.measure.ar.viewmodel  listOf com.measure.ar.viewmodel  plus com.measure.ar.viewmodel  
plusAssign com.measure.ar.viewmodel  until com.measure.ar.viewmodel  viewModelScope com.measure.ar.viewmodel  Boolean 'com.measure.ar.viewmodel.MeasureUiState  List 'com.measure.ar.viewmodel.MeasureUiState  MeasurementType 'com.measure.ar.viewmodel.MeasureUiState  Point3D 'com.measure.ar.viewmodel.MeasureUiState  String 'com.measure.ar.viewmodel.MeasureUiState  copy 'com.measure.ar.viewmodel.MeasureUiState  currentMeasurementType 'com.measure.ar.viewmodel.MeasureUiState  	emptyList 'com.measure.ar.viewmodel.MeasureUiState  isARInitialized 'com.measure.ar.viewmodel.MeasureUiState  
isCalibrating 'com.measure.ar.viewmodel.MeasureUiState  isMeasuring 'com.measure.ar.viewmodel.MeasureUiState  measurementPoints 'com.measure.ar.viewmodel.MeasureUiState  AREngine )com.measure.ar.viewmodel.MeasureViewModel  Application )com.measure.ar.viewmodel.MeasureViewModel  Float )com.measure.ar.viewmodel.MeasureViewModel  List )com.measure.ar.viewmodel.MeasureViewModel  Math )com.measure.ar.viewmodel.MeasureViewModel  MeasureUiState )com.measure.ar.viewmodel.MeasureViewModel  Measurement )com.measure.ar.viewmodel.MeasureViewModel  MeasurementQuality )com.measure.ar.viewmodel.MeasureViewModel  MeasurementSession )com.measure.ar.viewmodel.MeasureViewModel  MeasurementType )com.measure.ar.viewmodel.MeasureViewModel  MutableStateFlow )com.measure.ar.viewmodel.MeasureViewModel  Point3D )com.measure.ar.viewmodel.MeasureViewModel  	StateFlow )com.measure.ar.viewmodel.MeasureViewModel  String )com.measure.ar.viewmodel.MeasureViewModel  System )com.measure.ar.viewmodel.MeasureViewModel  _currentMeasurement )com.measure.ar.viewmodel.MeasureViewModel  _currentSession )com.measure.ar.viewmodel.MeasureViewModel  _uiState )com.measure.ar.viewmodel.MeasureViewModel  addMeasurementPoint )com.measure.ar.viewmodel.MeasureViewModel  addMeasurementToSession )com.measure.ar.viewmodel.MeasureViewModel  arEngine )com.measure.ar.viewmodel.MeasureViewModel  arSessionState )com.measure.ar.viewmodel.MeasureViewModel  asStateFlow )com.measure.ar.viewmodel.MeasureViewModel  calculateConfidence )com.measure.ar.viewmodel.MeasureViewModel  calculatePolygonArea )com.measure.ar.viewmodel.MeasureViewModel  checkCalibration )com.measure.ar.viewmodel.MeasureViewModel  clearMeasurements )com.measure.ar.viewmodel.MeasureViewModel  completeMeasurement )com.measure.ar.viewmodel.MeasureViewModel  createAreaMeasurement )com.measure.ar.viewmodel.MeasureViewModel  createLineMeasurement )com.measure.ar.viewmodel.MeasureViewModel  createMultiSegmentMeasurement )com.measure.ar.viewmodel.MeasureViewModel  currentMeasurement )com.measure.ar.viewmodel.MeasureViewModel  	emptyList )com.measure.ar.viewmodel.MeasureViewModel  first )com.measure.ar.viewmodel.MeasureViewModel  generateMeasurementId )com.measure.ar.viewmodel.MeasureViewModel  generateSessionId )com.measure.ar.viewmodel.MeasureViewModel  getASStateFlow )com.measure.ar.viewmodel.MeasureViewModel  getAsStateFlow )com.measure.ar.viewmodel.MeasureViewModel  getEMPTYList )com.measure.ar.viewmodel.MeasureViewModel  getEmptyList )com.measure.ar.viewmodel.MeasureViewModel  getFIRST )com.measure.ar.viewmodel.MeasureViewModel  getFirst )com.measure.ar.viewmodel.MeasureViewModel  
getISNotEmpty )com.measure.ar.viewmodel.MeasureViewModel  
getIsNotEmpty )com.measure.ar.viewmodel.MeasureViewModel  	getKOTLIN )com.measure.ar.viewmodel.MeasureViewModel  	getKotlin )com.measure.ar.viewmodel.MeasureViewModel  	getLAUNCH )com.measure.ar.viewmodel.MeasureViewModel  getLET )com.measure.ar.viewmodel.MeasureViewModel  	getLISTOf )com.measure.ar.viewmodel.MeasureViewModel  	getLaunch )com.measure.ar.viewmodel.MeasureViewModel  getLet )com.measure.ar.viewmodel.MeasureViewModel  	getListOf )com.measure.ar.viewmodel.MeasureViewModel  getPLUS )com.measure.ar.viewmodel.MeasureViewModel  
getPLUSAssign )com.measure.ar.viewmodel.MeasureViewModel  getPlus )com.measure.ar.viewmodel.MeasureViewModel  
getPlusAssign )com.measure.ar.viewmodel.MeasureViewModel  getUNTIL )com.measure.ar.viewmodel.MeasureViewModel  getUntil )com.measure.ar.viewmodel.MeasureViewModel  getVIEWModelScope )com.measure.ar.viewmodel.MeasureViewModel  getViewModelScope )com.measure.ar.viewmodel.MeasureViewModel  indices )com.measure.ar.viewmodel.MeasureViewModel  initializeAR )com.measure.ar.viewmodel.MeasureViewModel  invoke )com.measure.ar.viewmodel.MeasureViewModel  
isNotEmpty )com.measure.ar.viewmodel.MeasureViewModel  kotlin )com.measure.ar.viewmodel.MeasureViewModel  launch )com.measure.ar.viewmodel.MeasureViewModel  let )com.measure.ar.viewmodel.MeasureViewModel  listOf )com.measure.ar.viewmodel.MeasureViewModel  plus )com.measure.ar.viewmodel.MeasureViewModel  
plusAssign )com.measure.ar.viewmodel.MeasureViewModel  startCalibration )com.measure.ar.viewmodel.MeasureViewModel  startMeasurement )com.measure.ar.viewmodel.MeasureViewModel  trackingQuality )com.measure.ar.viewmodel.MeasureViewModel  uiState )com.measure.ar.viewmodel.MeasureViewModel  until )com.measure.ar.viewmodel.MeasureViewModel  viewModelScope )com.measure.ar.viewmodel.MeasureViewModel  ARCameraView 	java.lang  AREngine 	java.lang  	AROverlay 	java.lang  
ARRenderer 	java.lang  ARSessionState 	java.lang  ARTrackingState 	java.lang  	Alignment 	java.lang  	ArCoreApk 	java.lang  Arrangement 	java.lang  BackgroundRenderer 	java.lang  BottomMeasurementControls 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  CalibrationOverlay 	java.lang  Card 	java.lang  CardDefaults 	java.lang  CircleShape 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Color 	java.lang  Column 	java.lang  Config 	java.lang  Context 	java.lang  
Coordinates2d 	java.lang  DisplayRotationHelper 	java.lang  ExperimentalPermissionsApi 	java.lang  
FloatArray 	java.lang  FloatingActionButton 	java.lang  
FontWeight 	java.lang  GLES20 	java.lang  Icon 	java.lang  Icons 	java.lang  IntArray 	java.lang  LinearProgressIndicator 	java.lang  
LoadingScreen 	java.lang  
MaterialTheme 	java.lang  Math 	java.lang  
MeasureApp 	java.lang  MeasureBlue 	java.lang  MeasureTheme 	java.lang  MeasureUiState 	java.lang  Measurement 	java.lang  MeasurementInstructions 	java.lang  MeasurementQuality 	java.lang  MeasurementSession 	java.lang  MeasurementToolbar 	java.lang  MeasurementType 	java.lang  MeasurementTypeButton 	java.lang  Modifier 	java.lang  MotionEvent 	java.lang  MutableStateFlow 	java.lang  PI 	java.lang  PermissionRequestScreen 	java.lang  Plane 	java.lang  
PlaneRenderer 	java.lang  Point3D 	java.lang  PointCloudRenderer 	java.lang  QualityIndicator 	java.lang  RENDERMODE_CONTINUOUSLY 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Scaffold 	java.lang  Sensor 	java.lang  
SensorManager 	java.lang  Session 	java.lang  Spacer 	java.lang  System 	java.lang  Text 	java.lang  ToolMenu 	java.lang  TopStatusBar 	java.lang  
TrackingState 	java.lang  
UnitSystem 	java.lang  WindowCompat 	java.lang  _uiState 	java.lang  abs 	java.lang  acos 	java.lang  android 	java.lang  apply 	java.lang  arEngine 	java.lang  asStateFlow 	java.lang  
background 	java.lang  clip 	java.lang  coerceIn 	java.lang  com 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  first 	java.lang  floatArrayOf 	java.lang  format 	java.lang  height 	java.lang  indices 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  kotlin 	java.lang  kotlinx 	java.lang  last 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  	lowercase 	java.lang  maxOf 	java.lang  minOf 	java.lang  
mutableListOf 	java.lang  padding 	java.lang  plus 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  size 	java.lang  sqrt 	java.lang  sumOf 	java.lang  to 	java.lang  until 	java.lang  random java.lang.Math  currentTimeMillis java.lang.System  	EGLConfig javax.microedition.khronos.egl  GL10 #javax.microedition.khronos.opengles  ARCameraView kotlin  AREngine kotlin  	AROverlay kotlin  
ARRenderer kotlin  ARSessionState kotlin  ARTrackingState kotlin  	Alignment kotlin  Any kotlin  	ArCoreApk kotlin  Arrangement kotlin  BackgroundRenderer kotlin  Boolean kotlin  BottomMeasurementControls kotlin  Box kotlin  Build kotlin  Button kotlin  CalibrationOverlay kotlin  Card kotlin  CardDefaults kotlin  CircleShape kotlin  CircularProgressIndicator kotlin  Color kotlin  Column kotlin  Config kotlin  Context kotlin  
Coordinates2d kotlin  DisplayRotationHelper kotlin  Double kotlin  	Exception kotlin  ExperimentalPermissionsApi kotlin  Float kotlin  
FloatArray kotlin  FloatingActionButton kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GLES20 kotlin  Icon kotlin  Icons kotlin  Int kotlin  IntArray kotlin  LinearProgressIndicator kotlin  
LoadingScreen kotlin  Long kotlin  
MaterialTheme kotlin  Math kotlin  
MeasureApp kotlin  MeasureBlue kotlin  MeasureTheme kotlin  MeasureUiState kotlin  Measurement kotlin  MeasurementInstructions kotlin  MeasurementQuality kotlin  MeasurementSession kotlin  MeasurementToolbar kotlin  MeasurementType kotlin  MeasurementTypeButton kotlin  Modifier kotlin  MotionEvent kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  PI kotlin  Pair kotlin  PermissionRequestScreen kotlin  Plane kotlin  
PlaneRenderer kotlin  Point3D kotlin  PointCloudRenderer kotlin  QualityIndicator kotlin  RENDERMODE_CONTINUOUSLY kotlin  RoundedCornerShape kotlin  Row kotlin  Scaffold kotlin  Sensor kotlin  
SensorManager kotlin  Session kotlin  Spacer kotlin  String kotlin  System kotlin  Text kotlin  ToolMenu kotlin  TopStatusBar kotlin  
TrackingState kotlin  Unit kotlin  
UnitSystem kotlin  WindowCompat kotlin  _uiState kotlin  abs kotlin  acos kotlin  android kotlin  apply kotlin  arEngine kotlin  asStateFlow kotlin  
background kotlin  clip kotlin  coerceIn kotlin  com kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  first kotlin  floatArrayOf kotlin  format kotlin  height kotlin  indices kotlin  
isNotEmpty kotlin  java kotlin  kotlin kotlin  kotlinx kotlin  last kotlin  launch kotlin  let kotlin  listOf kotlin  	lowercase kotlin  maxOf kotlin  minOf kotlin  
mutableListOf kotlin  padding kotlin  plus kotlin  
plusAssign kotlin  provideDelegate kotlin  size kotlin  sqrt kotlin  sumOf kotlin  to kotlin  until kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getCOERCEIn kotlin.Float  getCoerceIn kotlin.Float  
getPLUSAssign kotlin.Float  
getPlusAssign kotlin.Float  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  
component1 kotlin.Pair  
component2 kotlin.Pair  	getFORMAT 
kotlin.String  	getFormat 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getLOWERCASE 
kotlin.String  getLowercase 
kotlin.String  
isNotEmpty 
kotlin.String  ARCameraView kotlin.annotation  AREngine kotlin.annotation  	AROverlay kotlin.annotation  
ARRenderer kotlin.annotation  ARSessionState kotlin.annotation  ARTrackingState kotlin.annotation  	Alignment kotlin.annotation  	ArCoreApk kotlin.annotation  Arrangement kotlin.annotation  BackgroundRenderer kotlin.annotation  BottomMeasurementControls kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  CalibrationOverlay kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  CircleShape kotlin.annotation  CircularProgressIndicator kotlin.annotation  Color kotlin.annotation  Column kotlin.annotation  Config kotlin.annotation  Context kotlin.annotation  
Coordinates2d kotlin.annotation  DisplayRotationHelper kotlin.annotation  	Exception kotlin.annotation  ExperimentalPermissionsApi kotlin.annotation  
FloatArray kotlin.annotation  FloatingActionButton kotlin.annotation  
FontWeight kotlin.annotation  GLES20 kotlin.annotation  Icon kotlin.annotation  Icons kotlin.annotation  IntArray kotlin.annotation  LinearProgressIndicator kotlin.annotation  
LoadingScreen kotlin.annotation  
MaterialTheme kotlin.annotation  Math kotlin.annotation  
MeasureApp kotlin.annotation  MeasureBlue kotlin.annotation  MeasureTheme kotlin.annotation  MeasureUiState kotlin.annotation  Measurement kotlin.annotation  MeasurementInstructions kotlin.annotation  MeasurementQuality kotlin.annotation  MeasurementSession kotlin.annotation  MeasurementToolbar kotlin.annotation  MeasurementType kotlin.annotation  MeasurementTypeButton kotlin.annotation  Modifier kotlin.annotation  MotionEvent kotlin.annotation  MutableStateFlow kotlin.annotation  PI kotlin.annotation  PermissionRequestScreen kotlin.annotation  Plane kotlin.annotation  
PlaneRenderer kotlin.annotation  Point3D kotlin.annotation  PointCloudRenderer kotlin.annotation  QualityIndicator kotlin.annotation  RENDERMODE_CONTINUOUSLY kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Scaffold kotlin.annotation  Sensor kotlin.annotation  
SensorManager kotlin.annotation  Session kotlin.annotation  Spacer kotlin.annotation  System kotlin.annotation  Text kotlin.annotation  ToolMenu kotlin.annotation  TopStatusBar kotlin.annotation  
TrackingState kotlin.annotation  
UnitSystem kotlin.annotation  WindowCompat kotlin.annotation  _uiState kotlin.annotation  abs kotlin.annotation  acos kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arEngine kotlin.annotation  asStateFlow kotlin.annotation  
background kotlin.annotation  clip kotlin.annotation  coerceIn kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  first kotlin.annotation  floatArrayOf kotlin.annotation  format kotlin.annotation  height kotlin.annotation  indices kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  kotlin kotlin.annotation  kotlinx kotlin.annotation  last kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  maxOf kotlin.annotation  minOf kotlin.annotation  
mutableListOf kotlin.annotation  padding kotlin.annotation  plus kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  size kotlin.annotation  sqrt kotlin.annotation  sumOf kotlin.annotation  to kotlin.annotation  until kotlin.annotation  ARCameraView kotlin.collections  AREngine kotlin.collections  	AROverlay kotlin.collections  
ARRenderer kotlin.collections  ARSessionState kotlin.collections  ARTrackingState kotlin.collections  	Alignment kotlin.collections  	ArCoreApk kotlin.collections  Arrangement kotlin.collections  BackgroundRenderer kotlin.collections  BottomMeasurementControls kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  CalibrationOverlay kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  CircleShape kotlin.collections  CircularProgressIndicator kotlin.collections  
Collection kotlin.collections  Color kotlin.collections  Column kotlin.collections  Config kotlin.collections  Context kotlin.collections  
Coordinates2d kotlin.collections  DisplayRotationHelper kotlin.collections  	Exception kotlin.collections  ExperimentalPermissionsApi kotlin.collections  
FloatArray kotlin.collections  FloatingActionButton kotlin.collections  
FontWeight kotlin.collections  GLES20 kotlin.collections  Icon kotlin.collections  Icons kotlin.collections  IntArray kotlin.collections  LinearProgressIndicator kotlin.collections  List kotlin.collections  
LoadingScreen kotlin.collections  
MaterialTheme kotlin.collections  Math kotlin.collections  
MeasureApp kotlin.collections  MeasureBlue kotlin.collections  MeasureTheme kotlin.collections  MeasureUiState kotlin.collections  Measurement kotlin.collections  MeasurementInstructions kotlin.collections  MeasurementQuality kotlin.collections  MeasurementSession kotlin.collections  MeasurementToolbar kotlin.collections  MeasurementType kotlin.collections  MeasurementTypeButton kotlin.collections  Modifier kotlin.collections  MotionEvent kotlin.collections  MutableCollection kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  PI kotlin.collections  PermissionRequestScreen kotlin.collections  Plane kotlin.collections  
PlaneRenderer kotlin.collections  Point3D kotlin.collections  PointCloudRenderer kotlin.collections  QualityIndicator kotlin.collections  RENDERMODE_CONTINUOUSLY kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Scaffold kotlin.collections  Sensor kotlin.collections  
SensorManager kotlin.collections  Session kotlin.collections  Spacer kotlin.collections  System kotlin.collections  Text kotlin.collections  ToolMenu kotlin.collections  TopStatusBar kotlin.collections  
TrackingState kotlin.collections  
UnitSystem kotlin.collections  WindowCompat kotlin.collections  _uiState kotlin.collections  abs kotlin.collections  acos kotlin.collections  android kotlin.collections  apply kotlin.collections  arEngine kotlin.collections  asStateFlow kotlin.collections  
background kotlin.collections  clip kotlin.collections  coerceIn kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  first kotlin.collections  floatArrayOf kotlin.collections  format kotlin.collections  height kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  kotlin kotlin.collections  kotlinx kotlin.collections  last kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  padding kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  size kotlin.collections  sqrt kotlin.collections  sumOf kotlin.collections  sumOfDouble kotlin.collections  to kotlin.collections  until kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getFIRST kotlin.collections.List  getFirst kotlin.collections.List  
getINDICES kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIndices kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getLAST kotlin.collections.List  getLast kotlin.collections.List  getMAXOf kotlin.collections.List  getMINOf kotlin.collections.List  getMaxOf kotlin.collections.List  getMinOf kotlin.collections.List  getPLUS kotlin.collections.List  getPlus kotlin.collections.List  getSUMOf kotlin.collections.List  getSumOf kotlin.collections.List  
isNotEmpty kotlin.collections.List  ARCameraView kotlin.comparisons  AREngine kotlin.comparisons  	AROverlay kotlin.comparisons  
ARRenderer kotlin.comparisons  ARSessionState kotlin.comparisons  ARTrackingState kotlin.comparisons  	Alignment kotlin.comparisons  	ArCoreApk kotlin.comparisons  Arrangement kotlin.comparisons  BackgroundRenderer kotlin.comparisons  BottomMeasurementControls kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  CalibrationOverlay kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  CircleShape kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Color kotlin.comparisons  Column kotlin.comparisons  Config kotlin.comparisons  Context kotlin.comparisons  
Coordinates2d kotlin.comparisons  DisplayRotationHelper kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalPermissionsApi kotlin.comparisons  
FloatArray kotlin.comparisons  FloatingActionButton kotlin.comparisons  
FontWeight kotlin.comparisons  GLES20 kotlin.comparisons  Icon kotlin.comparisons  Icons kotlin.comparisons  IntArray kotlin.comparisons  LinearProgressIndicator kotlin.comparisons  
LoadingScreen kotlin.comparisons  
MaterialTheme kotlin.comparisons  Math kotlin.comparisons  
MeasureApp kotlin.comparisons  MeasureBlue kotlin.comparisons  MeasureTheme kotlin.comparisons  MeasureUiState kotlin.comparisons  Measurement kotlin.comparisons  MeasurementInstructions kotlin.comparisons  MeasurementQuality kotlin.comparisons  MeasurementSession kotlin.comparisons  MeasurementToolbar kotlin.comparisons  MeasurementType kotlin.comparisons  MeasurementTypeButton kotlin.comparisons  Modifier kotlin.comparisons  MotionEvent kotlin.comparisons  MutableStateFlow kotlin.comparisons  PI kotlin.comparisons  PermissionRequestScreen kotlin.comparisons  Plane kotlin.comparisons  
PlaneRenderer kotlin.comparisons  Point3D kotlin.comparisons  PointCloudRenderer kotlin.comparisons  QualityIndicator kotlin.comparisons  RENDERMODE_CONTINUOUSLY kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Scaffold kotlin.comparisons  Sensor kotlin.comparisons  
SensorManager kotlin.comparisons  Session kotlin.comparisons  Spacer kotlin.comparisons  System kotlin.comparisons  Text kotlin.comparisons  ToolMenu kotlin.comparisons  TopStatusBar kotlin.comparisons  
TrackingState kotlin.comparisons  
UnitSystem kotlin.comparisons  WindowCompat kotlin.comparisons  _uiState kotlin.comparisons  abs kotlin.comparisons  acos kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arEngine kotlin.comparisons  asStateFlow kotlin.comparisons  
background kotlin.comparisons  clip kotlin.comparisons  coerceIn kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  first kotlin.comparisons  floatArrayOf kotlin.comparisons  format kotlin.comparisons  height kotlin.comparisons  indices kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  kotlin kotlin.comparisons  kotlinx kotlin.comparisons  last kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  maxOf kotlin.comparisons  minOf kotlin.comparisons  
mutableListOf kotlin.comparisons  padding kotlin.comparisons  plus kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  size kotlin.comparisons  sqrt kotlin.comparisons  sumOf kotlin.comparisons  to kotlin.comparisons  until kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ARCameraView 	kotlin.io  AREngine 	kotlin.io  	AROverlay 	kotlin.io  
ARRenderer 	kotlin.io  ARSessionState 	kotlin.io  ARTrackingState 	kotlin.io  	Alignment 	kotlin.io  	ArCoreApk 	kotlin.io  Arrangement 	kotlin.io  BackgroundRenderer 	kotlin.io  BottomMeasurementControls 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  CalibrationOverlay 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  CircleShape 	kotlin.io  CircularProgressIndicator 	kotlin.io  Color 	kotlin.io  Column 	kotlin.io  Config 	kotlin.io  Context 	kotlin.io  
Coordinates2d 	kotlin.io  DisplayRotationHelper 	kotlin.io  	Exception 	kotlin.io  ExperimentalPermissionsApi 	kotlin.io  
FloatArray 	kotlin.io  FloatingActionButton 	kotlin.io  
FontWeight 	kotlin.io  GLES20 	kotlin.io  Icon 	kotlin.io  Icons 	kotlin.io  IntArray 	kotlin.io  LinearProgressIndicator 	kotlin.io  
LoadingScreen 	kotlin.io  
MaterialTheme 	kotlin.io  Math 	kotlin.io  
MeasureApp 	kotlin.io  MeasureBlue 	kotlin.io  MeasureTheme 	kotlin.io  MeasureUiState 	kotlin.io  Measurement 	kotlin.io  MeasurementInstructions 	kotlin.io  MeasurementQuality 	kotlin.io  MeasurementSession 	kotlin.io  MeasurementToolbar 	kotlin.io  MeasurementType 	kotlin.io  MeasurementTypeButton 	kotlin.io  Modifier 	kotlin.io  MotionEvent 	kotlin.io  MutableStateFlow 	kotlin.io  PI 	kotlin.io  PermissionRequestScreen 	kotlin.io  Plane 	kotlin.io  
PlaneRenderer 	kotlin.io  Point3D 	kotlin.io  PointCloudRenderer 	kotlin.io  QualityIndicator 	kotlin.io  RENDERMODE_CONTINUOUSLY 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Scaffold 	kotlin.io  Sensor 	kotlin.io  
SensorManager 	kotlin.io  Session 	kotlin.io  Spacer 	kotlin.io  System 	kotlin.io  Text 	kotlin.io  ToolMenu 	kotlin.io  TopStatusBar 	kotlin.io  
TrackingState 	kotlin.io  
UnitSystem 	kotlin.io  WindowCompat 	kotlin.io  _uiState 	kotlin.io  abs 	kotlin.io  acos 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arEngine 	kotlin.io  asStateFlow 	kotlin.io  
background 	kotlin.io  clip 	kotlin.io  coerceIn 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  first 	kotlin.io  floatArrayOf 	kotlin.io  format 	kotlin.io  height 	kotlin.io  indices 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  kotlin 	kotlin.io  kotlinx 	kotlin.io  last 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  maxOf 	kotlin.io  minOf 	kotlin.io  
mutableListOf 	kotlin.io  padding 	kotlin.io  plus 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  size 	kotlin.io  sqrt 	kotlin.io  sumOf 	kotlin.io  to 	kotlin.io  until 	kotlin.io  ARCameraView 
kotlin.jvm  AREngine 
kotlin.jvm  	AROverlay 
kotlin.jvm  
ARRenderer 
kotlin.jvm  ARSessionState 
kotlin.jvm  ARTrackingState 
kotlin.jvm  	Alignment 
kotlin.jvm  	ArCoreApk 
kotlin.jvm  Arrangement 
kotlin.jvm  BackgroundRenderer 
kotlin.jvm  BottomMeasurementControls 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  CalibrationOverlay 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  CircleShape 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Color 
kotlin.jvm  Column 
kotlin.jvm  Config 
kotlin.jvm  Context 
kotlin.jvm  
Coordinates2d 
kotlin.jvm  DisplayRotationHelper 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalPermissionsApi 
kotlin.jvm  
FloatArray 
kotlin.jvm  FloatingActionButton 
kotlin.jvm  
FontWeight 
kotlin.jvm  GLES20 
kotlin.jvm  Icon 
kotlin.jvm  Icons 
kotlin.jvm  IntArray 
kotlin.jvm  LinearProgressIndicator 
kotlin.jvm  
LoadingScreen 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Math 
kotlin.jvm  
MeasureApp 
kotlin.jvm  MeasureBlue 
kotlin.jvm  MeasureTheme 
kotlin.jvm  MeasureUiState 
kotlin.jvm  Measurement 
kotlin.jvm  MeasurementInstructions 
kotlin.jvm  MeasurementQuality 
kotlin.jvm  MeasurementSession 
kotlin.jvm  MeasurementToolbar 
kotlin.jvm  MeasurementType 
kotlin.jvm  MeasurementTypeButton 
kotlin.jvm  Modifier 
kotlin.jvm  MotionEvent 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  PI 
kotlin.jvm  PermissionRequestScreen 
kotlin.jvm  Plane 
kotlin.jvm  
PlaneRenderer 
kotlin.jvm  Point3D 
kotlin.jvm  PointCloudRenderer 
kotlin.jvm  QualityIndicator 
kotlin.jvm  RENDERMODE_CONTINUOUSLY 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Scaffold 
kotlin.jvm  Sensor 
kotlin.jvm  
SensorManager 
kotlin.jvm  Session 
kotlin.jvm  Spacer 
kotlin.jvm  System 
kotlin.jvm  Text 
kotlin.jvm  ToolMenu 
kotlin.jvm  TopStatusBar 
kotlin.jvm  
TrackingState 
kotlin.jvm  
UnitSystem 
kotlin.jvm  WindowCompat 
kotlin.jvm  _uiState 
kotlin.jvm  abs 
kotlin.jvm  acos 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arEngine 
kotlin.jvm  asStateFlow 
kotlin.jvm  
background 
kotlin.jvm  clip 
kotlin.jvm  coerceIn 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  first 
kotlin.jvm  floatArrayOf 
kotlin.jvm  format 
kotlin.jvm  height 
kotlin.jvm  indices 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  kotlin 
kotlin.jvm  kotlinx 
kotlin.jvm  last 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  maxOf 
kotlin.jvm  minOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  padding 
kotlin.jvm  plus 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  size 
kotlin.jvm  sqrt 
kotlin.jvm  sumOf 
kotlin.jvm  to 
kotlin.jvm  until 
kotlin.jvm  PI kotlin.math  Point3D kotlin.math  abs kotlin.math  acos kotlin.math  coerceIn kotlin.math  first kotlin.math  indices kotlin.math  last kotlin.math  maxOf kotlin.math  minOf kotlin.math  
mutableListOf kotlin.math  
plusAssign kotlin.math  sqrt kotlin.math  sumOf kotlin.math  until kotlin.math  ARCameraView 
kotlin.ranges  AREngine 
kotlin.ranges  	AROverlay 
kotlin.ranges  
ARRenderer 
kotlin.ranges  ARSessionState 
kotlin.ranges  ARTrackingState 
kotlin.ranges  	Alignment 
kotlin.ranges  	ArCoreApk 
kotlin.ranges  Arrangement 
kotlin.ranges  BackgroundRenderer 
kotlin.ranges  BottomMeasurementControls 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  CalibrationOverlay 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  CircleShape 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Color 
kotlin.ranges  Column 
kotlin.ranges  Config 
kotlin.ranges  Context 
kotlin.ranges  
Coordinates2d 
kotlin.ranges  DisplayRotationHelper 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalPermissionsApi 
kotlin.ranges  
FloatArray 
kotlin.ranges  FloatingActionButton 
kotlin.ranges  
FontWeight 
kotlin.ranges  GLES20 
kotlin.ranges  Icon 
kotlin.ranges  Icons 
kotlin.ranges  IntArray 
kotlin.ranges  IntRange 
kotlin.ranges  LinearProgressIndicator 
kotlin.ranges  
LoadingScreen 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Math 
kotlin.ranges  
MeasureApp 
kotlin.ranges  MeasureBlue 
kotlin.ranges  MeasureTheme 
kotlin.ranges  MeasureUiState 
kotlin.ranges  Measurement 
kotlin.ranges  MeasurementInstructions 
kotlin.ranges  MeasurementQuality 
kotlin.ranges  MeasurementSession 
kotlin.ranges  MeasurementToolbar 
kotlin.ranges  MeasurementType 
kotlin.ranges  MeasurementTypeButton 
kotlin.ranges  Modifier 
kotlin.ranges  MotionEvent 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  PI 
kotlin.ranges  PermissionRequestScreen 
kotlin.ranges  Plane 
kotlin.ranges  
PlaneRenderer 
kotlin.ranges  Point3D 
kotlin.ranges  PointCloudRenderer 
kotlin.ranges  QualityIndicator 
kotlin.ranges  RENDERMODE_CONTINUOUSLY 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Scaffold 
kotlin.ranges  Sensor 
kotlin.ranges  
SensorManager 
kotlin.ranges  Session 
kotlin.ranges  Spacer 
kotlin.ranges  System 
kotlin.ranges  Text 
kotlin.ranges  ToolMenu 
kotlin.ranges  TopStatusBar 
kotlin.ranges  
TrackingState 
kotlin.ranges  
UnitSystem 
kotlin.ranges  WindowCompat 
kotlin.ranges  _uiState 
kotlin.ranges  abs 
kotlin.ranges  acos 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arEngine 
kotlin.ranges  asStateFlow 
kotlin.ranges  
background 
kotlin.ranges  clip 
kotlin.ranges  coerceIn 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  first 
kotlin.ranges  floatArrayOf 
kotlin.ranges  format 
kotlin.ranges  height 
kotlin.ranges  indices 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  kotlin 
kotlin.ranges  kotlinx 
kotlin.ranges  last 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  maxOf 
kotlin.ranges  minOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  padding 
kotlin.ranges  plus 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  size 
kotlin.ranges  sqrt 
kotlin.ranges  sumOf 
kotlin.ranges  to 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ARCameraView kotlin.sequences  AREngine kotlin.sequences  	AROverlay kotlin.sequences  
ARRenderer kotlin.sequences  ARSessionState kotlin.sequences  ARTrackingState kotlin.sequences  	Alignment kotlin.sequences  	ArCoreApk kotlin.sequences  Arrangement kotlin.sequences  BackgroundRenderer kotlin.sequences  BottomMeasurementControls kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  CalibrationOverlay kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  CircleShape kotlin.sequences  CircularProgressIndicator kotlin.sequences  Color kotlin.sequences  Column kotlin.sequences  Config kotlin.sequences  Context kotlin.sequences  
Coordinates2d kotlin.sequences  DisplayRotationHelper kotlin.sequences  	Exception kotlin.sequences  ExperimentalPermissionsApi kotlin.sequences  
FloatArray kotlin.sequences  FloatingActionButton kotlin.sequences  
FontWeight kotlin.sequences  GLES20 kotlin.sequences  Icon kotlin.sequences  Icons kotlin.sequences  IntArray kotlin.sequences  LinearProgressIndicator kotlin.sequences  
LoadingScreen kotlin.sequences  
MaterialTheme kotlin.sequences  Math kotlin.sequences  
MeasureApp kotlin.sequences  MeasureBlue kotlin.sequences  MeasureTheme kotlin.sequences  MeasureUiState kotlin.sequences  Measurement kotlin.sequences  MeasurementInstructions kotlin.sequences  MeasurementQuality kotlin.sequences  MeasurementSession kotlin.sequences  MeasurementToolbar kotlin.sequences  MeasurementType kotlin.sequences  MeasurementTypeButton kotlin.sequences  Modifier kotlin.sequences  MotionEvent kotlin.sequences  MutableStateFlow kotlin.sequences  PI kotlin.sequences  PermissionRequestScreen kotlin.sequences  Plane kotlin.sequences  
PlaneRenderer kotlin.sequences  Point3D kotlin.sequences  PointCloudRenderer kotlin.sequences  QualityIndicator kotlin.sequences  RENDERMODE_CONTINUOUSLY kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Scaffold kotlin.sequences  Sensor kotlin.sequences  
SensorManager kotlin.sequences  Session kotlin.sequences  Spacer kotlin.sequences  System kotlin.sequences  Text kotlin.sequences  ToolMenu kotlin.sequences  TopStatusBar kotlin.sequences  
TrackingState kotlin.sequences  
UnitSystem kotlin.sequences  WindowCompat kotlin.sequences  _uiState kotlin.sequences  abs kotlin.sequences  acos kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arEngine kotlin.sequences  asStateFlow kotlin.sequences  
background kotlin.sequences  clip kotlin.sequences  coerceIn kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  first kotlin.sequences  floatArrayOf kotlin.sequences  format kotlin.sequences  height kotlin.sequences  indices kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  kotlin kotlin.sequences  kotlinx kotlin.sequences  last kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  
mutableListOf kotlin.sequences  padding kotlin.sequences  plus kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  size kotlin.sequences  sqrt kotlin.sequences  sumOf kotlin.sequences  to kotlin.sequences  until kotlin.sequences  ARCameraView kotlin.text  AREngine kotlin.text  	AROverlay kotlin.text  
ARRenderer kotlin.text  ARSessionState kotlin.text  ARTrackingState kotlin.text  	Alignment kotlin.text  	ArCoreApk kotlin.text  Arrangement kotlin.text  BackgroundRenderer kotlin.text  BottomMeasurementControls kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  CalibrationOverlay kotlin.text  Card kotlin.text  CardDefaults kotlin.text  CircleShape kotlin.text  CircularProgressIndicator kotlin.text  Color kotlin.text  Column kotlin.text  Config kotlin.text  Context kotlin.text  
Coordinates2d kotlin.text  DisplayRotationHelper kotlin.text  	Exception kotlin.text  ExperimentalPermissionsApi kotlin.text  
FloatArray kotlin.text  FloatingActionButton kotlin.text  
FontWeight kotlin.text  GLES20 kotlin.text  Icon kotlin.text  Icons kotlin.text  IntArray kotlin.text  LinearProgressIndicator kotlin.text  
LoadingScreen kotlin.text  
MaterialTheme kotlin.text  Math kotlin.text  
MeasureApp kotlin.text  MeasureBlue kotlin.text  MeasureTheme kotlin.text  MeasureUiState kotlin.text  Measurement kotlin.text  MeasurementInstructions kotlin.text  MeasurementQuality kotlin.text  MeasurementSession kotlin.text  MeasurementToolbar kotlin.text  MeasurementType kotlin.text  MeasurementTypeButton kotlin.text  Modifier kotlin.text  MotionEvent kotlin.text  MutableStateFlow kotlin.text  PI kotlin.text  PermissionRequestScreen kotlin.text  Plane kotlin.text  
PlaneRenderer kotlin.text  Point3D kotlin.text  PointCloudRenderer kotlin.text  QualityIndicator kotlin.text  RENDERMODE_CONTINUOUSLY kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Scaffold kotlin.text  Sensor kotlin.text  
SensorManager kotlin.text  Session kotlin.text  Spacer kotlin.text  System kotlin.text  Text kotlin.text  ToolMenu kotlin.text  TopStatusBar kotlin.text  
TrackingState kotlin.text  
UnitSystem kotlin.text  WindowCompat kotlin.text  _uiState kotlin.text  abs kotlin.text  acos kotlin.text  android kotlin.text  apply kotlin.text  arEngine kotlin.text  asStateFlow kotlin.text  
background kotlin.text  clip kotlin.text  coerceIn kotlin.text  com kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  first kotlin.text  floatArrayOf kotlin.text  format kotlin.text  height kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  kotlin kotlin.text  kotlinx kotlin.text  last kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  	lowercase kotlin.text  maxOf kotlin.text  minOf kotlin.text  
mutableListOf kotlin.text  padding kotlin.text  plus kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  size kotlin.text  sqrt kotlin.text  sumOf kotlin.text  to kotlin.text  until kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  _uiState !kotlinx.coroutines.CoroutineScope  arEngine !kotlinx.coroutines.CoroutineScope  getAREngine !kotlinx.coroutines.CoroutineScope  getArEngine !kotlinx.coroutines.CoroutineScope  
getKOTLINX !kotlinx.coroutines.CoroutineScope  
getKotlinx !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  get_uiState !kotlinx.coroutines.CoroutineScope  	isGranted !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  AREngine kotlinx.coroutines.flow  Math kotlinx.coroutines.flow  MeasureUiState kotlinx.coroutines.flow  Measurement kotlinx.coroutines.flow  MeasurementQuality kotlinx.coroutines.flow  MeasurementSession kotlinx.coroutines.flow  MeasurementType kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  Point3D kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  System kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  arEngine kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  first kotlinx.coroutines.flow  indices kotlinx.coroutines.flow  
isNotEmpty kotlinx.coroutines.flow  kotlin kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  let kotlinx.coroutines.flow  listOf kotlinx.coroutines.flow  plus kotlinx.coroutines.flow  
plusAssign kotlinx.coroutines.flow  until kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  	GLES11Ext android.opengl  GL_TEXTURE_EXTERNAL_OES android.opengl.GLES11Ext  GL_COMPILE_STATUS android.opengl.GLES20  GL_FLOAT android.opengl.GLES20  GL_FRAGMENT_SHADER android.opengl.GLES20  GL_LINK_STATUS android.opengl.GLES20  GL_TEXTURE0 android.opengl.GLES20  GL_TRIANGLE_STRIP android.opengl.GLES20  GL_VERTEX_SHADER android.opengl.GLES20  glActiveTexture android.opengl.GLES20  glAttachShader android.opengl.GLES20  glCompileShader android.opengl.GLES20  glCreateProgram android.opengl.GLES20  glCreateShader android.opengl.GLES20  glDeleteProgram android.opengl.GLES20  glDeleteShader android.opengl.GLES20  glDepthMask android.opengl.GLES20  glDisableVertexAttribArray android.opengl.GLES20  glDrawArrays android.opengl.GLES20  glEnableVertexAttribArray android.opengl.GLES20  glGetAttribLocation android.opengl.GLES20  glGetProgramiv android.opengl.GLES20  
glGetShaderiv android.opengl.GLES20  glGetUniformLocation android.opengl.GLES20  
glLinkProgram android.opengl.GLES20  glShaderSource android.opengl.GLES20  glUniform1i android.opengl.GLES20  glUseProgram android.opengl.GLES20  glVertexAttribPointer android.opengl.GLES20  also android.opengl.GLSurfaceView  also android.view.SurfaceView  also android.view.View  	ArCoreApk androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  	GLES11Ext androidx.compose.runtime  also androidx.compose.runtime  isDepthModeSupported androidx.compose.runtime  
trimIndent androidx.compose.runtime  	onDispose .androidx.compose.runtime.DisposableEffectScope  DisposableEffect com.google.ar.core  	GLES11Ext com.google.ar.core  also com.google.ar.core  getValue com.google.ar.core  isDepthModeSupported com.google.ar.core  mutableStateOf com.google.ar.core  provideDelegate com.google.ar.core  remember com.google.ar.core  setValue com.google.ar.core  
trimIndent com.google.ar.core  getISDepthModeSupported com.google.ar.core.Config  getIsDepthModeSupported com.google.ar.core.Config  isDepthModeSupported com.google.ar.core.Config  DISABLED #com.google.ar.core.Config.DepthMode  	ArCoreApk com.measure.ar.ui.components  DisposableEffect com.measure.ar.ui.components  	GLES11Ext com.measure.ar.ui.components  also com.measure.ar.ui.components  isDepthModeSupported com.measure.ar.ui.components  
trimIndent com.measure.ar.ui.components  also ,com.measure.ar.ui.components.ARGLSurfaceView  getALSO ,com.measure.ar.ui.components.ARGLSurfaceView  getAlso ,com.measure.ar.ui.components.ARGLSurfaceView  onPause ,com.measure.ar.ui.components.ARGLSurfaceView  onResume ,com.measure.ar.ui.components.ARGLSurfaceView  	ArCoreApk 'com.measure.ar.ui.components.ARRenderer  isDepthModeSupported 'com.measure.ar.ui.components.ARRenderer  	Exception /com.measure.ar.ui.components.BackgroundRenderer  	GLES11Ext /com.measure.ar.ui.components.BackgroundRenderer  Int /com.measure.ar.ui.components.BackgroundRenderer  String /com.measure.ar.ui.components.BackgroundRenderer  apply /com.measure.ar.ui.components.BackgroundRenderer  createShaderProgram /com.measure.ar.ui.components.BackgroundRenderer  getAPPLY /com.measure.ar.ui.components.BackgroundRenderer  getApply /com.measure.ar.ui.components.BackgroundRenderer  getJAVA /com.measure.ar.ui.components.BackgroundRenderer  getJava /com.measure.ar.ui.components.BackgroundRenderer  
getTRIMIndent /com.measure.ar.ui.components.BackgroundRenderer  
getTrimIndent /com.measure.ar.ui.components.BackgroundRenderer  java /com.measure.ar.ui.components.BackgroundRenderer  
loadShader /com.measure.ar.ui.components.BackgroundRenderer  quadPositionParam /com.measure.ar.ui.components.BackgroundRenderer  quadProgram /com.measure.ar.ui.components.BackgroundRenderer  quadTexCoordBuffer /com.measure.ar.ui.components.BackgroundRenderer  quadTexCoordParam /com.measure.ar.ui.components.BackgroundRenderer  quadVertexBuffer /com.measure.ar.ui.components.BackgroundRenderer  textureParam /com.measure.ar.ui.components.BackgroundRenderer  
trimIndent /com.measure.ar.ui.components.BackgroundRenderer  	GLES11Ext 	java.lang  also 	java.lang  isDepthModeSupported 	java.lang  
trimIndent 	java.lang  Buffer java.nio  
ByteBuffer java.nio  	ByteOrder java.nio  FloatBuffer java.nio  apply java.nio.Buffer  
asFloatBuffer java.nio.Buffer  order java.nio.Buffer  position java.nio.Buffer  put java.nio.Buffer  allocateDirect java.nio.ByteBuffer  
asFloatBuffer java.nio.ByteBuffer  order java.nio.ByteBuffer  nativeOrder java.nio.ByteOrder  apply java.nio.FloatBuffer  getAPPLY java.nio.FloatBuffer  getApply java.nio.FloatBuffer  position java.nio.FloatBuffer  put java.nio.FloatBuffer  	GLES11Ext kotlin  also kotlin  isDepthModeSupported kotlin  
trimIndent kotlin  
getTRIMIndent 
kotlin.String  
getTrimIndent 
kotlin.String  	GLES11Ext kotlin.annotation  also kotlin.annotation  isDepthModeSupported kotlin.annotation  
trimIndent kotlin.annotation  	GLES11Ext kotlin.collections  also kotlin.collections  isDepthModeSupported kotlin.collections  
trimIndent kotlin.collections  	GLES11Ext kotlin.comparisons  also kotlin.comparisons  isDepthModeSupported kotlin.comparisons  
trimIndent kotlin.comparisons  	GLES11Ext 	kotlin.io  also 	kotlin.io  isDepthModeSupported 	kotlin.io  
trimIndent 	kotlin.io  	GLES11Ext 
kotlin.jvm  also 
kotlin.jvm  isDepthModeSupported 
kotlin.jvm  
trimIndent 
kotlin.jvm  	GLES11Ext 
kotlin.ranges  also 
kotlin.ranges  isDepthModeSupported 
kotlin.ranges  
trimIndent 
kotlin.ranges  	GLES11Ext kotlin.sequences  also kotlin.sequences  isDepthModeSupported kotlin.sequences  
trimIndent kotlin.sequences  	GLES11Ext kotlin.text  also kotlin.text  isDepthModeSupported kotlin.text  
trimIndent kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               