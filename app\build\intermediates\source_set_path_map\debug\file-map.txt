com.measure.ar.app-core-1.10.1-0 C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\res
com.measure.ar.app-camera-view-1.2.3-1 C:\Users\<USER>\.gradle\caches\transforms-3\0680b79097ef8fc5aeaed9adf5e353a2\transformed\camera-view-1.2.3\res
com.measure.ar.app-foundation-1.4.3-2 C:\Users\<USER>\.gradle\caches\transforms-3\080af24b86e7a736fb1d2c984e82b7d9\transformed\foundation-1.4.3\res
com.measure.ar.app-core-1.39.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\res
com.measure.ar.app-ui-tooling-preview-1.4.3-4 C:\Users\<USER>\.gradle\caches\transforms-3\090eebd3d32e73be9b29a435a3806f19\transformed\ui-tooling-preview-1.4.3\res
com.measure.ar.app-lifecycle-runtime-2.6.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\19e4030d9138be6659621bb8f10fc4d8\transformed\lifecycle-runtime-2.6.1\res
com.measure.ar.app-ui-test-manifest-1.4.3-6 C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\res
com.measure.ar.app-animation-core-1.4.3-7 C:\Users\<USER>\.gradle\caches\transforms-3\247b15fb3656b83390c338c9acf24a5d\transformed\animation-core-1.4.3\res
com.measure.ar.app-savedstate-1.2.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\267a358f6732eaa66a728f3aae7ef582\transformed\savedstate-1.2.1\res
com.measure.ar.app-foundation-layout-1.4.3-9 C:\Users\<USER>\.gradle\caches\transforms-3\2e2013cd2a1ebee7c237b884a99e710e\transformed\foundation-layout-1.4.3\res
com.measure.ar.app-ui-1.4.3-10 C:\Users\<USER>\.gradle\caches\transforms-3\2f933205bace544606e07716f0ee6247\transformed\ui-1.4.3\res
com.measure.ar.app-ui-text-1.4.3-11 C:\Users\<USER>\.gradle\caches\transforms-3\3b7fbc2d3b16ce5f21357a1f8a807645\transformed\ui-text-1.4.3\res
com.measure.ar.app-activity-ktx-1.7.2-12 C:\Users\<USER>\.gradle\caches\transforms-3\3bfbc17fc8a77550b40416dafcf83e54\transformed\activity-ktx-1.7.2\res
com.measure.ar.app-material-ripple-1.4.3-13 C:\Users\<USER>\.gradle\caches\transforms-3\43d3c5043fe024bcdb18099973601d18\transformed\material-ripple-1.4.3\res
com.measure.ar.app-lifecycle-viewmodel-ktx-2.6.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\480c28e510a3eb34ebe93f33b9eb1b6a\transformed\lifecycle-viewmodel-ktx-2.6.1\res
com.measure.ar.app-savedstate-ktx-1.2.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\49a20fe668bf08b4f0874cc61af9ae3c\transformed\savedstate-ktx-1.2.1\res
com.measure.ar.app-navigation-common-2.6.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\4afb199f8169321ff870be33d1a94c7d\transformed\navigation-common-2.6.0\res
com.measure.ar.app-lifecycle-runtime-ktx-2.6.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\4eaaabcb6df0d2483ee61c2c7a7d10e6\transformed\lifecycle-runtime-ktx-2.6.1\res
com.measure.ar.app-lifecycle-process-2.6.1-18 C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\res
com.measure.ar.app-runtime-saveable-1.4.3-19 C:\Users\<USER>\.gradle\caches\transforms-3\5adb8eea175570bf13de2e33f0d35e70\transformed\runtime-saveable-1.4.3\res
com.measure.ar.app-lifecycle-livedata-2.6.1-20 C:\Users\<USER>\.gradle\caches\transforms-3\5f438c4d62abd4ef0e7fa7a9171f3a99\transformed\lifecycle-livedata-2.6.1\res
com.measure.ar.app-ui-geometry-1.4.3-21 C:\Users\<USER>\.gradle\caches\transforms-3\5f837dc2e580a9121c71e88ce1bd7515\transformed\ui-geometry-1.4.3\res
com.measure.ar.app-lifecycle-viewmodel-2.6.1-22 C:\Users\<USER>\.gradle\caches\transforms-3\6004c5fc0ef70de5dbc64d4996f27a7b\transformed\lifecycle-viewmodel-2.6.1\res
com.measure.ar.app-activity-1.7.2-23 C:\Users\<USER>\.gradle\caches\transforms-3\6657ccc8b5733031a445c86dd277f1e3\transformed\activity-1.7.2\res
com.measure.ar.app-core-runtime-2.2.0-24 C:\Users\<USER>\.gradle\caches\transforms-3\7107afd79c504ad9b646039c49df771a\transformed\core-runtime-2.2.0\res
com.measure.ar.app-lifecycle-viewmodel-compose-2.6.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\71dec1ee4cd0b03b81f745c0cb6bda4e\transformed\lifecycle-viewmodel-compose-2.6.1\res
com.measure.ar.app-navigation-runtime-2.6.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\72b4e606400e8fbe6689466b853a46c7\transformed\navigation-runtime-2.6.0\res
com.measure.ar.app-core-ktx-1.10.1-27 C:\Users\<USER>\.gradle\caches\transforms-3\76536efdb59467b2f42d891a4d02cccf\transformed\core-ktx-1.10.1\res
com.measure.ar.app-material-1.4.3-28 C:\Users\<USER>\.gradle\caches\transforms-3\783b1771d7d4fa7637c00d77f327f6f7\transformed\material-1.4.3\res
com.measure.ar.app-appcompat-1.1.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\7dd1b3a9f23fc52e969f73e7935c2a62\transformed\appcompat-1.1.0\res
com.measure.ar.app-startup-runtime-1.1.1-30 C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\res
com.measure.ar.app-camera-camera2-1.2.3-31 C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\res
com.measure.ar.app-customview-poolingcontainer-1.0.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\89826be8eb5db12a93d8900fb913d71c\transformed\customview-poolingcontainer-1.0.0\res
com.measure.ar.app-material-icons-core-1.4.3-33 C:\Users\<USER>\.gradle\caches\transforms-3\8a76693bcf85482c2907b9bb520aee8a\transformed\material-icons-core-1.4.3\res
com.measure.ar.app-navigation-runtime-ktx-2.6.0-34 C:\Users\<USER>\.gradle\caches\transforms-3\8c8de9522c5601c5c4191942ee422185\transformed\navigation-runtime-ktx-2.6.0\res
com.measure.ar.app-animation-1.4.3-35 C:\Users\<USER>\.gradle\caches\transforms-3\990b427b3b7c13ccce1212e67eb42f60\transformed\animation-1.4.3\res
com.measure.ar.app-ui-tooling-1.4.3-36 C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\res
com.measure.ar.app-ui-util-1.4.3-37 C:\Users\<USER>\.gradle\caches\transforms-3\9cf4d73608557c13a2334b7b5e07448a\transformed\ui-util-1.4.3\res
com.measure.ar.app-navigation-compose-2.6.0-38 C:\Users\<USER>\.gradle\caches\transforms-3\acbdce7833fa3726cbbd9257a9ca9cfd\transformed\navigation-compose-2.6.0\res
com.measure.ar.app-runtime-1.4.3-39 C:\Users\<USER>\.gradle\caches\transforms-3\af82cc9f5e3603381a170f2db6b05b1c\transformed\runtime-1.4.3\res
com.measure.ar.app-appcompat-resources-1.1.0-40 C:\Users\<USER>\.gradle\caches\transforms-3\b016eae976ad658305d804badf89321b\transformed\appcompat-resources-1.1.0\res
com.measure.ar.app-annotation-experimental-1.3.0-41 C:\Users\<USER>\.gradle\caches\transforms-3\b0fafc20f315e4de42cf14ab8ac94f56\transformed\annotation-experimental-1.3.0\res
com.measure.ar.app-emoji2-1.3.0-42 C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\res
com.measure.ar.app-navigation-common-ktx-2.6.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\c0d7db3122fa77f45ef09003065bf7e8\transformed\navigation-common-ktx-2.6.0\res
com.measure.ar.app-profileinstaller-1.3.0-44 C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\res
com.measure.ar.app-camera-lifecycle-1.2.3-45 C:\Users\<USER>\.gradle\caches\transforms-3\d625ef4a8e1c099c507118cf27c90eab\transformed\camera-lifecycle-1.2.3\res
com.measure.ar.app-ui-unit-1.4.3-46 C:\Users\<USER>\.gradle\caches\transforms-3\d909281ac61eae8c7e7efb504f9cf765\transformed\ui-unit-1.4.3\res
com.measure.ar.app-camera-core-1.2.3-47 C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\res
com.measure.ar.app-material3-1.1.1-48 C:\Users\<USER>\.gradle\caches\transforms-3\dad28f5405e8ecd83a477663f8c93b32\transformed\material3-1.1.1\res
com.measure.ar.app-ui-tooling-data-1.4.3-49 C:\Users\<USER>\.gradle\caches\transforms-3\e227eadf7be79548f6e8e78f88680e43\transformed\ui-tooling-data-1.4.3\res
com.measure.ar.app-lifecycle-livedata-core-2.6.1-50 C:\Users\<USER>\.gradle\caches\transforms-3\e2d12991baf79c8e1f44a4805ba7ca42\transformed\lifecycle-livedata-core-2.6.1\res
com.measure.ar.app-lifecycle-viewmodel-savedstate-2.6.1-51 C:\Users\<USER>\.gradle\caches\transforms-3\e8cac778c5b5014b48464f1fb9ec0e5f\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.measure.ar.app-activity-compose-1.7.2-52 C:\Users\<USER>\.gradle\caches\transforms-3\f9634d3098cf4280fd9535641350d796\transformed\activity-compose-1.7.2\res
com.measure.ar.app-ui-graphics-1.4.3-53 C:\Users\<USER>\.gradle\caches\transforms-3\fa313ba462e989ede190af542803e7b9\transformed\ui-graphics-1.4.3\res
com.measure.ar.app-resValues-54 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\generated\res\resValues\debug
com.measure.ar.app-packageDebugResources-55 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.measure.ar.app-packageDebugResources-56 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.measure.ar.app-merged_res-57 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\intermediates\merged_res\debug
com.measure.ar.app-debug-58 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\debug\res
com.measure.ar.app-main-59 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res
