{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b179e72bf03cadde01e4099557985561\\transformed\\material3-1.1.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,279,397,508,585,680,793,927,1040,1173,1253,1348,1436,1528,1641,1761,1861,1997,2129,2269,2434,2556,2673,2795,2913,3002,3098,3214,3334,3429,3528,3630,3765,3908,4015,4109,4181,4259,4348,4431,4541,4617,4700,4799,4900,4987,5084,5168,5270,5365,5462,5576,5652,5751", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "161,274,392,503,580,675,788,922,1035,1168,1248,1343,1431,1523,1636,1756,1856,1992,2124,2264,2429,2551,2668,2790,2908,2997,3093,3209,3329,3424,3523,3625,3760,3903,4010,4104,4176,4254,4343,4426,4536,4612,4695,4794,4895,4982,5079,5163,5265,5360,5457,5571,5647,5746,5839"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3233,3344,3457,3575,4603,4680,4775,4888,5022,5135,5268,5348,5443,5531,5623,5736,5856,5956,6092,6224,6364,6529,6651,6768,6890,7008,7097,7193,7309,7429,7524,7623,7725,7860,8003,8110,8418,8567,9299,9543,9727,10107,10183,10266,10365,10466,10553,10650,10734,10836,10931,11028,11142,11218,11317", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "3339,3452,3570,3681,4675,4770,4883,5017,5130,5263,5343,5438,5526,5618,5731,5851,5951,6087,6219,6359,6524,6646,6763,6885,7003,7092,7188,7304,7424,7519,7618,7720,7855,7998,8105,8199,8485,8640,9383,9621,9832,10178,10261,10360,10461,10548,10645,10729,10831,10926,11023,11137,11213,11312,11405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,285,394,505", "endColumns": "46,47,108,110,80", "endOffsets": "236,284,393,504,585"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,208,321,436", "endColumns": "50,51,112,114,84", "endOffsets": "151,203,316,431,516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3c15b22be3cd3ef6196522864286995\\transformed\\ui-1.4.3\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,990,1060,1143,1230,1302,1384,1452", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,985,1055,1138,1225,1297,1379,1447,1567"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4423,4518,8204,8313,8490,8645,8722,8815,8905,8988,9059,9129,9212,9471,9837,9919,9987", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "4513,4598,8308,8413,8562,8717,8810,8900,8983,9054,9124,9207,9294,9538,9914,9982,10102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "521,636,738,846,932,1039,1158,1237,1313,1404,1497,1592,1686,1787,1880,1975,2070,2161,2252,2334,2443,2543,2642,2751,2863,2974,3137,9388", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "631,733,841,927,1034,1153,1232,1308,1399,1492,1587,1681,1782,1875,1970,2065,2156,2247,2329,2438,2538,2637,2746,2858,2969,3132,3228,9466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c919e02dd627ce1aec5be10fe930459e\\transformed\\core-1.10.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3686,3783,3885,3983,4082,4196,4301,9626", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3778,3880,3978,4077,4191,4296,4418,9722"}}]}]}