# Testing Plan - Measure AR App

## Testing Strategy Overview

### Testing Pyramid
```
                    ┌─────────────┐
                    │   Manual    │ ← 10% (Exploratory, Usability)
                    │   Testing   │
                ┌───┴─────────────┴───┐
                │   Integration       │ ← 20% (AR, Sensors, UI)
                │     Testing         │
            ┌───┴─────────────────────┴───┐
            │      Unit Testing           │ ← 70% (Logic, Calculations)
            └─────────────────────────────┘
```

### Testing Objectives
1. **Accuracy Validation**: Ensure sub-centimeter measurement precision
2. **Performance Verification**: Maintain 60 FPS and responsive UI
3. **Reliability Testing**: Stable operation across devices and conditions
4. **Usability Validation**: Intuitive interface for all user types

## Unit Testing

### Measurement Calculations
```kotlin
class MeasurementCalculationTest {
    @Test
    fun `distance calculation accuracy`() {
        val point1 = Point3D(0f, 0f, 0f)
        val point2 = Point3D(1f, 0f, 0f)
        val distance = point1.distanceTo(point2)
        
        assertThat(distance).isWithin(0.001f).of(1.0f)
    }
    
    @Test
    fun `area calculation for rectangle`() {
        val points = listOf(
            Point3D(0f, 0f, 0f),
            Point3D(2f, 0f, 0f),
            Point3D(2f, 0f, 3f),
            Point3D(0f, 0f, 3f)
        )
        val area = calculatePolygonArea(points)
        
        assertThat(area).isWithin(0.01f).of(6.0f)
    }
    
    @Test
    fun `confidence calculation based on quality`() {
        val confidence = calculateConfidence(MeasurementQuality.GOOD)
        assertThat(confidence).isEqualTo(0.9f)
    }
}
```

### Sensor Fusion Testing
```kotlin
class SensorFusionTest {
    @Test
    fun `kalman filter noise reduction`() {
        val noisyData = generateNoisyAccelerometerData()
        val filtered = kalmanFilter.filter(noisyData)
        
        val noiseReduction = calculateNoiseReduction(noisyData, filtered)
        assertThat(noiseReduction).isGreaterThan(0.8f)
    }
    
    @Test
    fun `orientation fusion accuracy`() {
        val mockSensorData = createMockSensorData()
        val orientation = sensorFusion.calculateOrientation(mockSensorData)
        
        assertThat(orientation.accuracy).isGreaterThan(0.95f)
    }
}
```

### Data Model Testing
```kotlin
class MeasurementDataTest {
    @Test
    fun `measurement serialization`() {
        val measurement = createTestMeasurement()
        val json = measurement.toJson()
        val deserialized = Measurement.fromJson(json)
        
        assertThat(deserialized).isEqualTo(measurement)
    }
    
    @Test
    fun `unit conversion accuracy`() {
        val measurement = Measurement(value = 1.0f, unit = "m")
        val inCentimeters = measurement.convertTo("cm")
        
        assertThat(inCentimeters.value).isEqualTo(100.0f)
    }
}
```

## Integration Testing

### AR Engine Integration
```kotlin
class AREngineIntegrationTest {
    @Test
    fun `ar session lifecycle`() {
        val arEngine = AREngine(context)
        
        // Test initialization
        val initSuccess = arEngine.initialize()
        assertThat(initSuccess).isTrue()
        
        // Test session operations
        arEngine.resume()
        val frame = arEngine.updateSession()
        assertThat(frame).isNotNull()
        
        // Test cleanup
        arEngine.pause()
        arEngine.cleanup()
    }
    
    @Test
    fun `hit test accuracy`() {
        val arEngine = createInitializedAREngine()
        val hitResults = arEngine.hitTest(screenX = 100f, screenY = 200f)
        
        assertThat(hitResults).isNotEmpty()
        assertThat(hitResults.first().distance).isGreaterThan(0f)
    }
}
```

### Camera Integration
```kotlin
class CameraIntegrationTest {
    @Test
    fun `camera permission handling`() {
        val permissionState = mockPermissionState(granted = false)
        val screen = createMeasureScreen(permissionState)
        
        screen.requestPermission()
        verify(permissionState).launchPermissionRequest()
    }
    
    @Test
    fun `camera preview rendering`() {
        val cameraView = createARCameraView()
        val renderingStarted = cameraView.startPreview()
        
        assertThat(renderingStarted).isTrue()
        assertThat(cameraView.isRendering).isTrue()
    }
}
```

### UI Integration Testing
```kotlin
class UIIntegrationTest {
    @Test
    fun `measurement flow integration`() {
        val viewModel = createTestViewModel()
        val screen = createMeasureScreen(viewModel)
        
        // Start measurement
        screen.selectMeasurementType(MeasurementType.SINGLE_LINE)
        assertThat(viewModel.uiState.value.isMeasuring).isTrue()
        
        // Add points
        screen.tapAt(100f, 200f)
        screen.tapAt(300f, 400f)
        
        // Verify measurement completion
        assertThat(viewModel.currentMeasurement.value).isNotNull()
    }
}
```

## Performance Testing

### Frame Rate Testing
```kotlin
class PerformanceTest {
    @Test
    fun `maintain_60_fps_during_measurement`() {
        val frameRateMonitor = FrameRateMonitor()
        val arRenderer = createARRenderer()
        
        // Simulate 5 seconds of rendering
        repeat(300) {
            val frameStart = System.nanoTime()
            arRenderer.onDrawFrame()
            frameRateMonitor.recordFrame(frameStart)
        }
        
        val averageFPS = frameRateMonitor.getAverageFPS()
        assertThat(averageFPS).isGreaterThan(55.0) // Allow 5 FPS tolerance
    }
    
    @Test
    fun `memory_usage_within_limits`() {
        val memoryMonitor = MemoryMonitor()
        val measureApp = createMeasureApp()
        
        // Perform 100 measurements
        repeat(100) {
            measureApp.performMeasurement()
            memoryMonitor.recordUsage()
        }
        
        val maxMemoryUsage = memoryMonitor.getMaxUsage()
        assertThat(maxMemoryUsage).isLessThan(200 * 1024 * 1024) // 200MB limit
    }
}
```

### Battery Testing
```kotlin
class BatteryPerformanceTest {
    @Test
    fun `battery_drain_within_acceptable_limits`() {
        val batteryMonitor = BatteryMonitor()
        val measureApp = createMeasureApp()
        
        batteryMonitor.startMonitoring()
        
        // Run app for 1 hour simulation
        measureApp.simulateUsage(duration = Duration.ofHours(1))
        
        val batteryDrain = batteryMonitor.getBatteryDrain()
        assertThat(batteryDrain).isLessThan(0.15) // 15% max drain per hour
    }
}
```

## Accuracy Testing

### Real-World Measurement Validation
```kotlin
class AccuracyTest {
    @Test
    fun `measurement_accuracy_validation`() {
        val testScenes = loadPhysicalTestScenes()
        val accuracyResults = mutableListOf<AccuracyResult>()
        
        testScenes.forEach { scene ->
            val measuredDistance = performMeasurement(scene)
            val actualDistance = scene.groundTruthDistance
            val error = abs(measuredDistance - actualDistance)
            
            accuracyResults.add(AccuracyResult(
                scene = scene.name,
                measured = measuredDistance,
                actual = actualDistance,
                error = error,
                relativeError = error / actualDistance
            ))
        }
        
        // Validate accuracy requirements
        val averageError = accuracyResults.map { it.error }.average()
        assertThat(averageError).isLessThan(0.005) // 5mm average error
        
        val maxError = accuracyResults.maxOf { it.error }
        assertThat(maxError).isLessThan(0.02) // 2cm max error
    }
}
```

### Environmental Condition Testing
```kotlin
class EnvironmentalTest {
    @Test
    fun `accuracy_under_various_lighting_conditions`() {
        val lightingConditions = listOf(
            LightingCondition.BRIGHT_SUNLIGHT,
            LightingCondition.OFFICE_LIGHTING,
            LightingCondition.DIM_LIGHTING,
            LightingCondition.MIXED_LIGHTING
        )
        
        lightingConditions.forEach { condition ->
            val accuracy = measureAccuracyUnderLighting(condition)
            
            when (condition) {
                LightingCondition.OFFICE_LIGHTING -> 
                    assertThat(accuracy).isGreaterThan(0.95)
                LightingCondition.DIM_LIGHTING -> 
                    assertThat(accuracy).isGreaterThan(0.80)
                else -> 
                    assertThat(accuracy).isGreaterThan(0.85)
            }
        }
    }
}
```

## Usability Testing

### User Task Testing
```kotlin
class UsabilityTest {
    @Test
    fun `first_time_user_completion_rate`() {
        val testUsers = createFirstTimeUsers(count = 20)
        val completionResults = mutableListOf<TaskResult>()
        
        testUsers.forEach { user ->
            val result = user.performFirstMeasurementTask()
            completionResults.add(result)
        }
        
        val completionRate = completionResults.count { it.completed } / 
                           completionResults.size.toDouble()
        
        assertThat(completionRate).isGreaterThan(0.90) // 90% completion rate
    }
    
    @Test
    fun `time_to_first_measurement`() {
        val testUsers = createTestUsers(count = 10)
        val timeResults = mutableListOf<Duration>()
        
        testUsers.forEach { user ->
            val startTime = System.currentTimeMillis()
            user.completeFirstMeasurement()
            val endTime = System.currentTimeMillis()
            
            timeResults.add(Duration.ofMillis(endTime - startTime))
        }
        
        val averageTime = timeResults.map { it.toSeconds() }.average()
        assertThat(averageTime).isLessThan(30.0) // 30 seconds average
    }
}
```

## Device Compatibility Testing

### Device Matrix Testing
```
┌─────────────────┬──────────┬──────────┬──────────┬──────────┐
│ Device Category │ Low-End  │ Mid-Range│ High-End │ Flagship │
├─────────────────┼──────────┼──────────┼──────────┼──────────┤
│ Performance     │ 30+ FPS  │ 45+ FPS  │ 60 FPS   │ 60 FPS   │
│ Accuracy        │ ±2cm     │ ±1cm     │ ±0.5cm   │ ±0.2cm   │
│ Features        │ Basic    │ Standard │ Advanced │ Full     │
│ Battery Life    │ 2+ hours │ 3+ hours │ 4+ hours │ 5+ hours │
└─────────────────┴──────────┴──────────┴──────────┴──────────┘
```

### Test Device List
```
Low-End Devices:
- Samsung Galaxy A32 (Snapdragon 720G)
- Xiaomi Redmi Note 10 (Snapdragon 678)

Mid-Range Devices:
- Google Pixel 6a (Tensor)
- OnePlus Nord 2 (Dimensity 1200)

High-End Devices:
- Samsung Galaxy S22 (Snapdragon 8 Gen 1)
- iPhone 13 Pro (A15 Bionic)

Flagship Devices:
- Google Pixel 7 Pro (Tensor G2)
- Samsung Galaxy S23 Ultra (Snapdragon 8 Gen 2)
```

## Automated Testing Pipeline

### CI/CD Integration
```yaml
# GitHub Actions workflow
name: Measure App Testing
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run unit tests
        run: ./gradlew testDebugUnitTest
      
  integration-tests:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: ./gradlew connectedAndroidTest
        
  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run performance benchmarks
        run: ./gradlew benchmarkDebug
```

### Test Reporting
- **Coverage Reports**: JaCoCo for code coverage analysis
- **Performance Reports**: Automated benchmark results
- **Accuracy Reports**: Real-world measurement validation
- **Compatibility Reports**: Device-specific test results

## Test Data Management

### Test Scenarios
```kotlin
object TestScenarios {
    val BASIC_MEASUREMENTS = listOf(
        TestScene("ruler_30cm", groundTruth = 0.30f),
        TestScene("table_width", groundTruth = 1.20f),
        TestScene("door_height", groundTruth = 2.10f)
    )
    
    val COMPLEX_MEASUREMENTS = listOf(
        TestScene("room_area", groundTruth = 15.5f, type = AREA),
        TestScene("box_volume", groundTruth = 0.125f, type = VOLUME)
    )
}
```

### Mock Data Generation
```kotlin
class MockDataGenerator {
    fun generateARFrame(): Frame = mockk {
        every { camera.trackingState } returns TrackingState.TRACKING
        every { camera.pose } returns createMockPose()
        every { acquirePointCloud() } returns createMockPointCloud()
    }
    
    fun generateSensorData(): SensorData = SensorData(
        accelerometer = floatArrayOf(0f, 9.8f, 0f),
        gyroscope = floatArrayOf(0f, 0f, 0f),
        magnetometer = floatArrayOf(0f, 50f, 0f)
    )
}
```
