{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "13,14,15,16,17,18,19,21", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "644,714,798,882,978,1080,1182,1338", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "709,793,877,973,1075,1177,1271,1422"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Measure Claude\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "3,15", "startColumns": "4,4", "startOffsets": "153,783", "endLines": "13,15", "endColumns": "12,62", "endOffsets": "777,841"}, "to": {"startLines": "2,20", "startColumns": "4,4", "startOffsets": "55,1276", "endLines": "12,20", "endColumns": "12,61", "endOffsets": "639,1333"}}]}]}