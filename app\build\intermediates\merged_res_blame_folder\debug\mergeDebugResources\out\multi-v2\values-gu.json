{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c919e02dd627ce1aec5be10fe930459e\\transformed\\core-1.10.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3689,3783,3886,3983,4085,4187,4285,9637", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3778,3881,3978,4080,4182,4280,4402,9733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,287,408,534", "endColumns": "46,49,120,125,89", "endOffsets": "236,286,407,533,623"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,210,335,465", "endColumns": "50,53,124,129,93", "endOffsets": "151,205,330,460,554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "559,666,770,877,964,1064,1184,1262,1339,1430,1523,1618,1712,1812,1905,2000,2094,2185,2276,2356,2462,2563,2660,2769,2869,2979,3139,9398", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "661,765,872,959,1059,1179,1257,1334,1425,1518,1613,1707,1807,1900,1995,2089,2180,2271,2351,2457,2558,2655,2764,2864,2974,3134,3237,9474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3c15b22be3cd3ef6196522864286995\\transformed\\ui-1.4.3\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,986,1055,1138,1221,1296,1372,1438", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,981,1050,1133,1216,1291,1367,1433,1549"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4407,4499,8220,8313,8489,8654,8740,8841,8928,9014,9082,9151,9234,9479,9832,9908,9974", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "4494,4576,8308,8407,8571,8735,8836,8923,9009,9077,9146,9229,9312,9549,9903,9969,10085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b179e72bf03cadde01e4099557985561\\transformed\\material3-1.1.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,394,502,581,676,789,924,1040,1178,1259,1359,1449,1545,1655,1779,1884,2015,2143,2269,2444,2566,2684,2807,2939,3030,3122,3245,3371,3468,3569,3672,3802,3939,4044,4141,4218,4296,4377,4460,4554,4630,4710,4807,4906,5000,5096,5179,5281,5376,5474,5588,5664,5760", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "160,268,389,497,576,671,784,919,1035,1173,1254,1354,1444,1540,1650,1774,1879,2010,2138,2264,2439,2561,2679,2802,2934,3025,3117,3240,3366,3463,3564,3667,3797,3934,4039,4136,4213,4291,4372,4455,4549,4625,4705,4802,4901,4995,5091,5174,5276,5371,5469,5583,5659,5755,5845"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3242,3352,3460,3581,4581,4660,4755,4868,5003,5119,5257,5338,5438,5528,5624,5734,5858,5963,6094,6222,6348,6523,6645,6763,6886,7018,7109,7201,7324,7450,7547,7648,7751,7881,8018,8123,8412,8576,9317,9554,9738,10090,10166,10246,10343,10442,10536,10632,10715,10817,10912,11010,11124,11200,11296", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "3347,3455,3576,3684,4655,4750,4863,4998,5114,5252,5333,5433,5523,5619,5729,5853,5958,6089,6217,6343,6518,6640,6758,6881,7013,7104,7196,7319,7445,7542,7643,7746,7876,8013,8118,8215,8484,8649,9393,9632,9827,10161,10241,10338,10437,10531,10627,10710,10812,10907,11005,11119,11195,11291,11381"}}]}]}