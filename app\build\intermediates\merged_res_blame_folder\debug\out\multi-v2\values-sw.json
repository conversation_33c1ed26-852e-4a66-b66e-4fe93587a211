{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,390,500,578,674,788,924,1040,1188,1270,1366,1455,1553,1670,1793,1894,2025,2155,2298,2459,2608,2728,2854,2987,3077,3169,3287,3413,3506,3606,3712,3839,3976,4084,4179,4255,4334,4417,4498,4608,4689,4768,4863,4959,5057,5156,5240,5341,5437,5535,5656,5736,5850", "endColumns": "111,110,111,109,77,95,113,135,115,147,81,95,88,97,116,122,100,130,129,142,160,148,119,125,132,89,91,117,125,92,99,105,126,136,107,94,75,78,82,80,109,80,78,94,95,97,98,83,100,95,97,120,79,113,105", "endOffsets": "162,273,385,495,573,669,783,919,1035,1183,1265,1361,1450,1548,1665,1788,1889,2020,2150,2293,2454,2603,2723,2849,2982,3072,3164,3282,3408,3501,3601,3707,3834,3971,4079,4174,4250,4329,4412,4493,4603,4684,4763,4858,4954,5052,5151,5235,5336,5432,5530,5651,5731,5845,5951"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3283,3395,3506,3618,4626,4704,4800,4914,5050,5166,5314,5396,5492,5581,5679,5796,5919,6020,6151,6281,6424,6585,6734,6854,6980,7113,7203,7295,7413,7539,7632,7732,7838,7965,8102,8210,8507,8669,9416,9655,9837,10215,10296,10375,10470,10566,10664,10763,10847,10948,11044,11142,11263,11343,11457", "endColumns": "111,110,111,109,77,95,113,135,115,147,81,95,88,97,116,122,100,130,129,142,160,148,119,125,132,89,91,117,125,92,99,105,126,136,107,94,75,78,82,80,109,80,78,94,95,97,98,83,100,95,97,120,79,113,105", "endOffsets": "3390,3501,3613,3723,4699,4795,4909,5045,5161,5309,5391,5487,5576,5674,5791,5914,6015,6146,6276,6419,6580,6729,6849,6975,7108,7198,7290,7408,7534,7627,7727,7833,7960,8097,8205,8300,8578,8743,9494,9731,9942,10291,10370,10465,10561,10659,10758,10842,10943,11039,11137,11258,11338,11452,11558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,287,417,554", "endColumns": "46,49,129,136,97", "endOffsets": "236,286,416,553,651"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,210,344,485", "endColumns": "50,53,133,140,101", "endOffsets": "151,205,339,480,582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,993,1064,1149,1236,1310,1387,1457", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,988,1059,1144,1231,1305,1382,1452,1573"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4451,4545,8305,8406,8583,8748,8829,8930,9021,9103,9173,9244,9329,9581,9947,10024,10094", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "4540,4621,8401,8502,8664,8824,8925,9016,9098,9168,9239,9324,9411,9650,10019,10089,10210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,82,90,91,94,93,100,92,94,93,90,90,80,100,107,98,106,111,103,161,96,81", "endOffsets": "203,302,410,500,605,722,805,888,979,1071,1166,1260,1361,1454,1549,1643,1734,1825,1906,2007,2115,2214,2321,2433,2537,2699,2796,2878"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "587,690,789,897,987,1092,1209,1292,1375,1466,1558,1653,1747,1848,1941,2036,2130,2221,2312,2393,2494,2602,2701,2808,2920,3024,3186,9499", "endColumns": "102,98,107,89,104,116,82,82,90,91,94,93,100,92,94,93,90,90,80,100,107,98,106,111,103,161,96,81", "endOffsets": "685,784,892,982,1087,1204,1287,1370,1461,1553,1648,1742,1843,1936,2031,2125,2216,2307,2388,2489,2597,2696,2803,2915,3019,3181,3278,9576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3728,3822,3924,4021,4122,4229,4336,9736", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3817,3919,4016,4117,4224,4331,4446,9832"}}]}]}