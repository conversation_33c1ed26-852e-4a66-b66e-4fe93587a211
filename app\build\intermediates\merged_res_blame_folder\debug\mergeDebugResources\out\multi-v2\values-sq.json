{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,285,415,546", "endColumns": "45,48,129,130,93", "endOffsets": "235,284,414,545,639"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,155,208,342,477", "endColumns": "49,52,133,134,97", "endOffsets": "150,203,337,472,570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3c15b22be3cd3ef6196522864286995\\transformed\\ui-1.4.3\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4464,4559,8299,8398,8573,8746,8827,8920,9012,9102,9171,9238,9325,9580,9934,10011,10077", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "4554,4637,8393,8495,8664,8822,8915,9007,9097,9166,9233,9320,9411,9648,10006,10072,10193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b179e72bf03cadde01e4099557985561\\transformed\\material3-1.1.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,397,511,587,678,787,921,1033,1175,1255,1351,1439,1534,1648,1768,1869,2002,2132,2272,2457,2591,2710,2831,2954,3043,3135,3256,3393,3484,3587,3692,3826,3967,4074,4168,4241,4318,4400,4479,4580,4656,4735,4830,4927,5018,5112,5196,5301,5397,5495,5619,5695,5805", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "165,277,392,506,582,673,782,916,1028,1170,1250,1346,1434,1529,1643,1763,1864,1997,2127,2267,2452,2586,2705,2826,2949,3038,3130,3251,3388,3479,3582,3687,3821,3962,4069,4163,4236,4313,4395,4474,4575,4651,4730,4825,4922,5013,5107,5191,5296,5392,5490,5614,5690,5800,5903"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3271,3386,3498,3613,4642,4718,4809,4918,5052,5164,5306,5386,5482,5570,5665,5779,5899,6000,6133,6263,6403,6588,6722,6841,6962,7085,7174,7266,7387,7524,7615,7718,7823,7957,8098,8205,8500,8669,9416,9653,9833,10198,10274,10353,10448,10545,10636,10730,10814,10919,11015,11113,11237,11313,11423", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "3381,3493,3608,3722,4713,4804,4913,5047,5159,5301,5381,5477,5565,5660,5774,5894,5995,6128,6258,6398,6583,6717,6836,6957,7080,7169,7261,7382,7519,7610,7713,7818,7952,8093,8200,8294,8568,8741,9493,9727,9929,10269,10348,10443,10540,10631,10725,10809,10914,11010,11108,11232,11308,11418,11521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "575,689,789,901,987,1093,1216,1298,1376,1467,1560,1655,1749,1850,1943,2038,2135,2226,2319,2400,2506,2610,2708,2814,2918,3020,3174,9498", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "684,784,896,982,1088,1211,1293,1371,1462,1555,1650,1744,1845,1938,2033,2130,2221,2314,2395,2501,2605,2703,2809,2913,3015,3169,3266,9575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c919e02dd627ce1aec5be10fe930459e\\transformed\\core-1.10.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3727,3826,3928,4026,4123,4231,4342,9732", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3821,3923,4021,4118,4226,4337,4459,9828"}}]}]}