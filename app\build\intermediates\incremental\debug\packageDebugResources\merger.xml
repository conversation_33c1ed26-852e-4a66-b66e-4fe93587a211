<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_placeholder" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\drawable\ic_launcher_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="measure_blue">#FF2196F3</color><color name="measure_blue_dark">#FF1976D2</color><color name="measure_red">#FFF44336</color><color name="measure_green">#FF4CAF50</color><color name="measure_green_dark">#FF388E3C</color><color name="measure_orange">#FFFF9800</color><color name="ar_overlay">#80000000</color><color name="ar_reticle">#FFFFFFFF</color><color name="ar_measure_line">#FF00BCD4</color><color name="ar_snap_indicator">#FF8BC34A</color><color name="quality_poor">#FFF44336</color><color name="quality_fair">#FFFF9800</color><color name="quality_good">#FF4CAF50</color><color name="quality_excellent">#FF00BCD4</color></file><file path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Measure</string><string name="measurement_line">Line</string><string name="measurement_path">Path</string><string name="measurement_area">Area</string><string name="measurement_volume">Volume</string><string name="measurement_level">Level</string><string name="instruction_tap_first_point">Tap to set the first point</string><string name="instruction_tap_second_point">Tap to set the second point</string><string name="instruction_start_path">Tap to start path measurement</string><string name="instruction_continue_path">Tap to add more points</string><string name="instruction_start_area">Tap to start area measurement</string><string name="instruction_add_corner">Tap to add corner</string><string name="quality_poor">Poor</string><string name="quality_fair">Fair</string><string name="quality_good">Good</string><string name="quality_excellent">Excellent</string><string name="calibration_title">Calibrating Device</string><string name="calibration_instruction">Move your device in a figure-8 pattern</string><string name="calibration_complete">Calibration Complete</string><string name="permission_camera_title">Camera Permission Required</string><string name="permission_camera_description">This app needs camera access to measure objects using AR</string><string name="permission_grant">Grant Permission</string><string name="action_complete">Complete</string><string name="action_cancel">Cancel</string><string name="action_clear">Clear</string><string name="action_calibrate">Calibrate</string><string name="action_measure">Measure</string><string name="unit_cm">cm</string><string name="unit_m">m</string><string name="unit_in">in</string><string name="unit_ft">ft</string><string name="unit_m2">m²</string><string name="unit_m3">m³</string><string name="error_ar_not_supported">AR is not supported on this device</string><string name="error_camera_not_available">Camera is not available</string><string name="error_tracking_lost">AR tracking lost. Please move the device slowly</string><string name="loading_ar">Initializing AR...</string><string name="loading_calibration">Calibrating...</string></file><file path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Measure" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/measure_blue</item>
        <item name="colorPrimaryDark">@color/measure_blue_dark</item>
        <item name="colorAccent">@color/measure_green</item>

        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style><style name="Theme.Measure" parent="Base.Theme.Measure"/></file><file path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Measure" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/measure_blue</item>
        <item name="colorPrimaryDark">@color/measure_blue_dark</item>
        <item name="colorAccent">@color/measure_green</item>

        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style><style name="Theme.Measure" parent="Base.Theme.Measure"/></file><file name="backup_rules" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>