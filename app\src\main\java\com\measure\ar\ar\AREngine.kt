package com.measure.ar.ar

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import com.google.ar.core.*
import com.google.ar.core.exceptions.*
import com.measure.ar.data.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Core AR engine that manages ARCore session, sensor fusion, and measurement calculations
 */
class AREngine(private val context: Context) : SensorEventListener {
    
    private var arSession: Session? = null
    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    private val gyroscope = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE)
    private val magnetometer = sensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD)
    
    // State flows
    private val _sessionState = MutableStateFlow(ARSessionState())
    val sessionState: StateFlow<ARSessionState> = _sessionState.asStateFlow()
    
    private val _trackingQuality = MutableStateFlow(MeasurementQuality.POOR)
    val trackingQuality: StateFlow<MeasurementQuality> = _trackingQuality.asStateFlow()
    
    // Sensor data
    private var accelerometerData = FloatArray(3)
    private var gyroscopeData = FloatArray(3)
    private var magnetometerData = FloatArray(3)
    private var rotationMatrix = FloatArray(9)
    private var orientationAngles = FloatArray(3)
    
    // Calibration state
    private var isCalibrated = false
    private var calibrationSamples = 0
    private val requiredCalibrationSamples = 100
    
    /**
     * Initialize ARCore session
     */
    suspend fun initialize(): Boolean {
        return try {
            // Check if ARCore is supported
            when (ArCoreApk.getInstance().checkAvailability(context)) {
                ArCoreApk.Availability.SUPPORTED_INSTALLED -> {
                    createSession()
                    true
                }
                ArCoreApk.Availability.SUPPORTED_APK_TOO_OLD,
                ArCoreApk.Availability.SUPPORTED_NOT_INSTALLED -> {
                    // Request ARCore installation
                    false
                }
                else -> false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun createSession() {
        arSession = Session(context).apply {
            // Configure session
            val config = Config(this).apply {
                planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL
                lightEstimationMode = Config.LightEstimationMode.ENVIRONMENTAL_HDR
                depthMode = Config.DepthMode.AUTOMATIC
                instantPlacementMode = Config.InstantPlacementMode.LOCAL_Y_UP
            }
            configure(config)
        }
        
        // Start sensor listeners
        startSensorListening()
        
        _sessionState.value = _sessionState.value.copy(
            isInitialized = true,
            depthEnabled = true
        )
    }
    
    private fun startSensorListening() {
        accelerometer?.let { 
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_GAME)
        }
        gyroscope?.let { 
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_GAME)
        }
        magnetometer?.let { 
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_GAME)
        }
    }
    
    /**
     * Update AR session and get current frame
     */
    fun updateSession(): Frame? {
        return try {
            arSession?.update()
        } catch (e: CameraNotAvailableException) {
            null
        }
    }
    
    /**
     * Perform hit test at screen coordinates
     */
    fun hitTest(x: Float, y: Float): List<HitResult> {
        return arSession?.hitTest(x, y) ?: emptyList()
    }
    
    /**
     * Calculate distance between two hit results
     */
    fun calculateDistance(point1: HitResult, point2: HitResult): Measurement? {
        val pose1 = point1.hitPose
        val pose2 = point2.hitPose
        
        val p1 = Point3D(pose1.tx(), pose1.ty(), pose1.tz())
        val p2 = Point3D(pose2.tx(), pose2.ty(), pose2.tz())
        
        val distance = p1.distanceTo(p2)
        val quality = calculateMeasurementQuality(point1, point2)
        val confidence = calculateConfidence(quality)
        
        return Measurement(
            id = generateMeasurementId(),
            type = MeasurementType.SINGLE_LINE,
            points = listOf(p1, p2),
            value = distance,
            quality = quality,
            confidence = confidence
        )
    }
    
    /**
     * Calculate measurement quality based on tracking state and environmental factors
     */
    private fun calculateMeasurementQuality(vararg hitResults: HitResult): MeasurementQuality {
        val currentFrame = arSession?.update()
        val trackingState = currentFrame?.camera?.trackingState
        
        return when {
            trackingState != TrackingState.TRACKING -> MeasurementQuality.POOR
            !isCalibrated -> MeasurementQuality.FAIR
            _sessionState.value.lightEstimation < 0.3f -> MeasurementQuality.FAIR
            hitResults.any { it.distance > 5.0f } -> MeasurementQuality.FAIR
            else -> MeasurementQuality.GOOD
        }
    }
    
    private fun calculateConfidence(quality: MeasurementQuality): Float {
        return when (quality) {
            MeasurementQuality.POOR -> 0.5f
            MeasurementQuality.FAIR -> 0.7f
            MeasurementQuality.GOOD -> 0.9f
            MeasurementQuality.EXCELLENT -> 0.95f
        }
    }
    
    /**
     * Start calibration process
     */
    fun startCalibration() {
        isCalibrated = false
        calibrationSamples = 0
    }
    
    /**
     * Check if device is properly calibrated
     */
    fun isCalibrationComplete(): Boolean = isCalibrated
    
    override fun onSensorChanged(event: SensorEvent) {
        when (event.sensor.type) {
            Sensor.TYPE_ACCELEROMETER -> {
                accelerometerData = event.values.clone()
                updateCalibration()
            }
            Sensor.TYPE_GYROSCOPE -> {
                gyroscopeData = event.values.clone()
            }
            Sensor.TYPE_MAGNETIC_FIELD -> {
                magnetometerData = event.values.clone()
            }
        }
        
        // Update device orientation
        if (SensorManager.getRotationMatrix(rotationMatrix, null, accelerometerData, magnetometerData)) {
            SensorManager.getOrientation(rotationMatrix, orientationAngles)
        }
    }
    
    private fun updateCalibration() {
        if (!isCalibrated) {
            calibrationSamples++
            if (calibrationSamples >= requiredCalibrationSamples) {
                isCalibrated = true
                _trackingQuality.value = MeasurementQuality.GOOD
            }
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // Handle sensor accuracy changes
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        sensorManager.unregisterListener(this)
        arSession?.close()
        arSession = null
    }
    
    private fun generateMeasurementId(): String {
        return "measurement_${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
    }
    
    /**
     * Resume AR session
     */
    fun resume() {
        try {
            arSession?.resume()
        } catch (e: CameraNotAvailableException) {
            // Handle camera not available
        }
    }
    
    /**
     * Pause AR session
     */
    fun pause() {
        arSession?.pause()
    }
}
