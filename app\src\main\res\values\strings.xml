<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Measure</string>
    
    <!-- Measurement types -->
    <string name="measurement_line">Line</string>
    <string name="measurement_path">Path</string>
    <string name="measurement_area">Area</string>
    <string name="measurement_volume">Volume</string>
    <string name="measurement_level">Level</string>
    
    <!-- Instructions -->
    <string name="instruction_tap_first_point">Tap to set the first point</string>
    <string name="instruction_tap_second_point">Tap to set the second point</string>
    <string name="instruction_start_path">Tap to start path measurement</string>
    <string name="instruction_continue_path">Tap to add more points</string>
    <string name="instruction_start_area">Tap to start area measurement</string>
    <string name="instruction_add_corner">Tap to add corner</string>
    
    <!-- Quality indicators -->
    <string name="quality_poor">Poor</string>
    <string name="quality_fair">Fair</string>
    <string name="quality_good">Good</string>
    <string name="quality_excellent">Excellent</string>
    
    <!-- Calibration -->
    <string name="calibration_title">Calibrating Device</string>
    <string name="calibration_instruction">Move your device in a figure-8 pattern</string>
    <string name="calibration_complete">Calibration Complete</string>
    
    <!-- Permissions -->
    <string name="permission_camera_title">Camera Permission Required</string>
    <string name="permission_camera_description">This app needs camera access to measure objects using AR</string>
    <string name="permission_grant">Grant Permission</string>
    
    <!-- Actions -->
    <string name="action_complete">Complete</string>
    <string name="action_cancel">Cancel</string>
    <string name="action_clear">Clear</string>
    <string name="action_calibrate">Calibrate</string>
    <string name="action_measure">Measure</string>
    
    <!-- Units -->
    <string name="unit_cm">cm</string>
    <string name="unit_m">m</string>
    <string name="unit_in">in</string>
    <string name="unit_ft">ft</string>
    <string name="unit_m2">m²</string>
    <string name="unit_m3">m³</string>
    
    <!-- Errors -->
    <string name="error_ar_not_supported">AR is not supported on this device</string>
    <string name="error_camera_not_available">Camera is not available</string>
    <string name="error_tracking_lost">AR tracking lost. Please move the device slowly</string>
    
    <!-- Loading -->
    <string name="loading_ar">Initializing AR...</string>
    <string name="loading_calibration">Calibrating...</string>
</resources>
