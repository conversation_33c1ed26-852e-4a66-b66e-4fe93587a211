{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3684,3782,3884,3985,4084,4189,4296,9532", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3777,3879,3980,4079,4184,4291,4410,9628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,290,402,516", "endColumns": "46,52,111,113,83", "endOffsets": "236,289,401,515,599"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,213,329,447", "endColumns": "50,56,115,117,87", "endOffsets": "151,208,324,442,530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,980,1049,1131,1217,1289,1368,1436", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,975,1044,1126,1212,1284,1363,1431,1551"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4415,4508,8120,8214,8396,8565,8643,8735,8826,8907,8976,9045,9127,9378,9727,9806,9874", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "4503,4586,8209,8311,8483,8638,8730,8821,8902,8971,9040,9122,9208,9445,9801,9869,9989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,886,977,1069,1164,1258,1353,1446,1541,1638,1729,1820,1903,2007,2119,2218,2324,2435,2537,2700,2798", "endColumns": "106,101,108,85,104,116,77,76,90,91,94,93,94,92,94,96,90,90,82,103,111,98,105,110,101,162,97,81", "endOffsets": "207,309,418,504,609,726,804,881,972,1064,1159,1253,1348,1441,1536,1633,1724,1815,1898,2002,2114,2213,2319,2430,2532,2695,2793,2875"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "535,642,744,853,939,1044,1161,1239,1316,1407,1499,1594,1688,1783,1876,1971,2068,2159,2250,2333,2437,2549,2648,2754,2865,2967,3130,9296", "endColumns": "106,101,108,85,104,116,77,76,90,91,94,93,94,92,94,96,90,90,82,103,111,98,105,110,101,162,97,81", "endOffsets": "637,739,848,934,1039,1156,1234,1311,1402,1494,1589,1683,1778,1871,1966,2063,2154,2245,2328,2432,2544,2643,2749,2860,2962,3125,3223,9373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,397,511,587,678,787,917,1029,1160,1241,1337,1425,1518,1630,1748,1849,1978,2104,2238,2397,2521,2634,2755,2873,2962,3055,3168,3279,3373,3472,3576,3703,3840,3946,4040,4120,4197,4280,4362,4456,4532,4614,4711,4810,4903,5000,5084,5186,5281,5379,5494,5570,5670", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "164,277,392,506,582,673,782,912,1024,1155,1236,1332,1420,1513,1625,1743,1844,1973,2099,2233,2392,2516,2629,2750,2868,2957,3050,3163,3274,3368,3467,3571,3698,3835,3941,4035,4115,4192,4275,4357,4451,4527,4609,4706,4805,4898,4995,5079,5181,5276,5374,5489,5565,5665,5756"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3228,3342,3455,3570,4591,4667,4758,4867,4997,5109,5240,5321,5417,5505,5598,5710,5828,5929,6058,6184,6318,6477,6601,6714,6835,6953,7042,7135,7248,7359,7453,7552,7656,7783,7920,8026,8316,8488,9213,9450,9633,9994,10070,10152,10249,10348,10441,10538,10622,10724,10819,10917,11032,11108,11208", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "3337,3450,3565,3679,4662,4753,4862,4992,5104,5235,5316,5412,5500,5593,5705,5823,5924,6053,6179,6313,6472,6596,6709,6830,6948,7037,7130,7243,7354,7448,7547,7651,7778,7915,8021,8115,8391,8560,9291,9527,9722,10065,10147,10244,10343,10436,10533,10617,10719,10814,10912,11027,11103,11203,11294"}}]}]}