com.measure.ar:xml/backup_rules = 0x7f110000
com.measure.ar:styleable/ViewBackgroundHelper = 0x7f100036
com.measure.ar:styleable/Toolbar = 0x7f100034
com.measure.ar:styleable/SwitchCompat = 0x7f100032
com.measure.ar:styleable/PreviewView = 0x7f10002c
com.measure.ar:styleable/NavDeepLink = 0x7f100025
com.measure.ar:styleable/NavArgument = 0x7f100024
com.measure.ar:styleable/MenuView = 0x7f100022
com.measure.ar:styleable/GradientColorItem = 0x7f10001c
com.measure.ar:styleable/Fragment = 0x7f100019
com.measure.ar:styleable/FontFamily = 0x7f100017
com.measure.ar:styleable/CompoundButton = 0x7f100015
com.measure.ar:styleable/ButtonBarLayout = 0x7f100011
com.measure.ar:styleable/AppCompatEmojiHelper = 0x7f10000b
com.measure.ar:styleable/AnimatedStateListDrawableTransition = 0x7f10000a
com.measure.ar:styleable/AlertDialog = 0x7f100007
com.measure.ar:styleable/ActionMode = 0x7f100004
com.measure.ar:styleable/ActionMenuView = 0x7f100003
com.measure.ar:styleable/ActionBar = 0x7f100000
com.measure.ar:style/Widget.Compat.NotificationActionText = 0x7f0f0162
com.measure.ar:styleable/AnimatedStateListDrawableItem = 0x7f100009
com.measure.ar:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f0160
com.measure.ar:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0158
com.measure.ar:style/Widget.AppCompat.SeekBar = 0x7f0f0157
com.measure.ar:style/Widget.AppCompat.ProgressBar = 0x7f0f0150
com.measure.ar:style/Widget.AppCompat.PopupWindow = 0x7f0f014f
com.measure.ar:style/Widget.AppCompat.ListView.DropDown = 0x7f0f014b
com.measure.ar:style/Widget.AppCompat.ListView = 0x7f0f014a
com.measure.ar:style/Widget.AppCompat.Light.SearchView = 0x7f0f0146
com.measure.ar:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f013f
com.measure.ar:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f013e
com.measure.ar:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f013d
com.measure.ar:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f013c
com.measure.ar:style/Widget.AppCompat.Light.ActionButton = 0x7f0f013b
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0139
com.measure.ar:style/Widget.AppCompat.ImageButton = 0x7f0f0131
com.measure.ar:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f012e
com.measure.ar:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f012a
com.measure.ar:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0126
com.measure.ar:style/Widget.AppCompat.Button = 0x7f0f0123
com.measure.ar:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f011e
com.measure.ar:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f011c
com.measure.ar:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f0119
com.measure.ar:style/Widget.AppCompat.ActionBar = 0x7f0f0118
com.measure.ar:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f0115
com.measure.ar:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f0114
com.measure.ar:style/Theme.Measure = 0x7f0f010e
com.measure.ar:styleable/AppCompatSeekBar = 0x7f10000d
com.measure.ar:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f010c
com.measure.ar:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f010a
com.measure.ar:style/Theme.AppCompat.Light.Dialog = 0x7f0f0108
com.measure.ar:style/Theme.AppCompat.Light = 0x7f0f0106
com.measure.ar:style/Theme.AppCompat.Empty = 0x7f0f0105
com.measure.ar:style/Theme.AppCompat.Dialog.Alert = 0x7f0f0102
com.measure.ar:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f0100
com.measure.ar:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f00fe
com.measure.ar:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f00fc
com.measure.ar:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f00f7
com.measure.ar:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f00f5
com.measure.ar:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f00f2
com.measure.ar:style/TextAppearance.Compat.Notification = 0x7f0f00f0
com.measure.ar:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f00ed
com.measure.ar:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f00eb
com.measure.ar:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f00ea
com.measure.ar:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f00e9
com.measure.ar:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f00e8
com.measure.ar:styleable/RecycleListView = 0x7f10002d
com.measure.ar:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f00e6
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f00e5
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f00e3
com.measure.ar:styleable/PopupWindow = 0x7f10002a
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f00df
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f00dd
com.measure.ar:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f00d9
com.measure.ar:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f00d2
com.measure.ar:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f00d0
com.measure.ar:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f00cd
com.measure.ar:style/TextAppearance.AppCompat.Large = 0x7f0f00cb
com.measure.ar:style/TextAppearance.AppCompat.Inverse = 0x7f0f00ca
com.measure.ar:style/TextAppearance.AppCompat.Display3 = 0x7f0f00c7
com.measure.ar:styleable/CheckedTextView = 0x7f100013
com.measure.ar:style/TextAppearance.AppCompat.Button = 0x7f0f00c3
com.measure.ar:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f00bb
com.measure.ar:xml/data_extraction_rules = 0x7f110001
com.measure.ar:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f00b4
com.measure.ar:styleable/FragmentContainerView = 0x7f10001a
com.measure.ar:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f00b2
com.measure.ar:style/TextAppearance.AppCompat.Small = 0x7f0f00d6
com.measure.ar:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f00b1
com.measure.ar:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f00b0
com.measure.ar:style/Platform.V25.AppCompat.Light = 0x7f0f00ad
com.measure.ar:style/Platform.ThemeOverlay.AppCompat = 0x7f0f00a7
com.measure.ar:style/Platform.AppCompat.Light = 0x7f0f00a6
com.measure.ar:style/Platform.AppCompat = 0x7f0f00a5
com.measure.ar:style/FloatingDialogTheme = 0x7f0f00a3
com.measure.ar:style/DialogWindowTheme = 0x7f0f00a2
com.measure.ar:style/Base.Widget.AppCompat.Spinner = 0x7f0f009c
com.measure.ar:style/Base.Widget.AppCompat.SeekBar = 0x7f0f009a
com.measure.ar:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f0099
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f00e0
com.measure.ar:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f0097
com.measure.ar:style/Base.Widget.AppCompat.RatingBar = 0x7f0f0095
com.measure.ar:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f0091
com.measure.ar:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f0090
com.measure.ar:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f008c
com.measure.ar:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f0089
com.measure.ar:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0087
com.measure.ar:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0085
com.measure.ar:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f007e
com.measure.ar:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f007d
com.measure.ar:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f0079
com.measure.ar:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f0083
com.measure.ar:style/Base.Widget.AppCompat.Button.Small = 0x7f0f0078
com.measure.ar:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f0077
com.measure.ar:style/Base.Widget.AppCompat.Button = 0x7f0f0073
com.measure.ar:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0072
com.measure.ar:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f0071
com.measure.ar:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f006c
com.measure.ar:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f006a
com.measure.ar:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f0069
com.measure.ar:style/Base.Widget.AppCompat.ActionBar = 0x7f0f0068
com.measure.ar:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f0067
com.measure.ar:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f0066
com.measure.ar:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f0063
com.measure.ar:style/Base.V7.Theme.AppCompat = 0x7f0f0060
com.measure.ar:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f005d
com.measure.ar:style/Base.V23.Theme.AppCompat.Light = 0x7f0f005a
com.measure.ar:style/Base.V22.Theme.AppCompat.Light = 0x7f0f0058
com.measure.ar:style/Base.V22.Theme.AppCompat = 0x7f0f0057
com.measure.ar:style/Base.V21.Theme.AppCompat = 0x7f0f0052
com.measure.ar:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f004f
com.measure.ar:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f004d
com.measure.ar:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0049
com.measure.ar:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0046
com.measure.ar:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0045
com.measure.ar:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f0042
com.measure.ar:style/TextAppearance.AppCompat.Headline = 0x7f0f00c9
com.measure.ar:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f0041
com.measure.ar:style/Base.Theme.AppCompat.Dialog = 0x7f0f003e
com.measure.ar:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f003b
com.measure.ar:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f003a
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0038
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0037
com.measure.ar:style/Widget.AppCompat.Spinner = 0x7f0f0159
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0034
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0033
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f0031
com.measure.ar:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f00ec
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f002f
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f002e
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f002b
com.measure.ar:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0027
com.measure.ar:style/Base.TextAppearance.AppCompat.Title = 0x7f0f0025
com.measure.ar:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f0023
com.measure.ar:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f0022
com.measure.ar:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f0020
com.measure.ar:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f001f
com.measure.ar:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f001e
com.measure.ar:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f00be
com.measure.ar:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f001d
com.measure.ar:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f001b
com.measure.ar:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0019
com.measure.ar:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0018
com.measure.ar:style/TextAppearance.AppCompat.Display1 = 0x7f0f00c5
com.measure.ar:style/Base.V26.Theme.AppCompat = 0x7f0f005b
com.measure.ar:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f0015
com.measure.ar:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f0013
com.measure.ar:style/TextAppearance.AppCompat.Body1 = 0x7f0f00c1
com.measure.ar:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f0012
com.measure.ar:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f0011
com.measure.ar:style/Base.TextAppearance.AppCompat.Button = 0x7f0f000f
com.measure.ar:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f000d
com.measure.ar:styleable/ViewStubCompat = 0x7f100037
com.measure.ar:style/TextAppearance.AppCompat.Display4 = 0x7f0f00c8
com.measure.ar:style/Base.TextAppearance.AppCompat = 0x7f0f000c
com.measure.ar:style/Base.Animation.AppCompat.Tooltip = 0x7f0f0009
com.measure.ar:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f015e
com.measure.ar:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f0008
com.measure.ar:style/Animation.AppCompat.Tooltip = 0x7f0f0004
com.measure.ar:style/Animation.AppCompat.DropDownUp = 0x7f0f0003
com.measure.ar:style/Animation.AppCompat.Dialog = 0x7f0f0002
com.measure.ar:string/unit_m3 = 0x7f0e0098
com.measure.ar:string/unit_m2 = 0x7f0e0097
com.measure.ar:string/unit_m = 0x7f0e0096
com.measure.ar:string/unit_cm = 0x7f0e0093
com.measure.ar:string/time_picker_period_toggle_description = 0x7f0e008f
com.measure.ar:string/time_picker_minute_text_field = 0x7f0e008e
com.measure.ar:string/time_picker_minute_suffix = 0x7f0e008d
com.measure.ar:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f00d5
com.measure.ar:string/time_picker_minute_selection = 0x7f0e008c
com.measure.ar:string/time_picker_minute = 0x7f0e008b
com.measure.ar:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f010b
com.measure.ar:string/time_picker_hour_text_field = 0x7f0e008a
com.measure.ar:string/time_picker_hour_suffix = 0x7f0e0089
com.measure.ar:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f003f
com.measure.ar:string/time_picker_hour_24h_suffix = 0x7f0e0087
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f00e2
com.measure.ar:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f0053
com.measure.ar:string/time_picker_hour = 0x7f0e0086
com.measure.ar:string/time_picker_am = 0x7f0e0085
com.measure.ar:string/template_percent = 0x7f0e0084
com.measure.ar:string/switch_role = 0x7f0e0082
com.measure.ar:string/snackbar_dismiss = 0x7f0e007f
com.measure.ar:string/selected = 0x7f0e007e
com.measure.ar:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f0112
com.measure.ar:string/search_menu_title = 0x7f0e007d
com.measure.ar:styleable/AnimatedStateListDrawableCompat = 0x7f100008
com.measure.ar:string/search_bar_search = 0x7f0e007c
com.measure.ar:string/quality_poor = 0x7f0e0079
com.measure.ar:string/quality_excellent = 0x7f0e0076
com.measure.ar:string/permission_grant = 0x7f0e0075
com.measure.ar:string/permission_camera_title = 0x7f0e0074
com.measure.ar:string/off = 0x7f0e0071
com.measure.ar:string/measurement_level = 0x7f0e006b
com.measure.ar:string/measurement_area = 0x7f0e006a
com.measure.ar:string/loading_ar = 0x7f0e0067
com.measure.ar:style/TextAppearance.AppCompat.Display2 = 0x7f0f00c6
com.measure.ar:string/instruction_tap_first_point = 0x7f0e0065
com.measure.ar:string/instruction_start_path = 0x7f0e0064
com.measure.ar:string/instruction_start_area = 0x7f0e0063
com.measure.ar:string/error_tracking_lost = 0x7f0e005d
com.measure.ar:string/error_camera_not_available = 0x7f0e005c
com.measure.ar:string/error_ar_not_supported = 0x7f0e005b
com.measure.ar:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f00bc
com.measure.ar:string/dialog = 0x7f0e0059
com.measure.ar:string/default_popup_window_title = 0x7f0e0058
com.measure.ar:string/date_range_picker_start_headline = 0x7f0e0055
com.measure.ar:string/date_range_picker_scroll_to_next_month = 0x7f0e0053
com.measure.ar:string/date_range_picker_end_headline = 0x7f0e0052
com.measure.ar:string/time_picker_hour_selection = 0x7f0e0088
com.measure.ar:string/date_range_picker_day_in_range = 0x7f0e0051
com.measure.ar:string/date_picker_year_picker_pane_title = 0x7f0e004e
com.measure.ar:string/date_picker_title = 0x7f0e004c
com.measure.ar:string/date_picker_switch_to_input_mode = 0x7f0e0048
com.measure.ar:string/date_picker_switch_to_day_selection = 0x7f0e0047
com.measure.ar:string/date_picker_switch_to_calendar_mode = 0x7f0e0046
com.measure.ar:string/date_picker_no_selection_description = 0x7f0e0043
com.measure.ar:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f015b
com.measure.ar:string/date_picker_headline_description = 0x7f0e0041
com.measure.ar:string/date_picker_headline = 0x7f0e0040
com.measure.ar:string/date_input_title = 0x7f0e003f
com.measure.ar:string/date_input_no_input_description = 0x7f0e003e
com.measure.ar:string/date_input_invalid_not_allowed = 0x7f0e003b
com.measure.ar:string/date_input_invalid_for_pattern = 0x7f0e003a
com.measure.ar:string/date_input_headline_description = 0x7f0e0039
com.measure.ar:string/date_input_headline = 0x7f0e0038
com.measure.ar:string/close_drawer = 0x7f0e0035
com.measure.ar:string/call_notification_screening_text = 0x7f0e0034
com.measure.ar:string/call_notification_ongoing_text = 0x7f0e0033
com.measure.ar:string/call_notification_hang_up_action = 0x7f0e0031
com.measure.ar:string/call_notification_decline_action = 0x7f0e0030
com.measure.ar:string/call_notification_answer_action = 0x7f0e002e
com.measure.ar:string/calibration_title = 0x7f0e002d
com.measure.ar:string/calibration_complete = 0x7f0e002b
com.measure.ar:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0145
com.measure.ar:string/bottom_sheet_drag_handle_description = 0x7f0e0029
com.measure.ar:string/instruction_continue_path = 0x7f0e0062
com.measure.ar:string/app_name = 0x7f0e0026
com.measure.ar:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0065
com.measure.ar:string/androidx_startup = 0x7f0e0025
com.measure.ar:string/action_measure = 0x7f0e0024
com.measure.ar:string/action_complete = 0x7f0e0023
com.measure.ar:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0144
com.measure.ar:string/action_clear = 0x7f0e0022
com.measure.ar:string/action_cancel = 0x7f0e0021
com.measure.ar:string/abc_searchview_description_submit = 0x7f0e001b
com.measure.ar:string/abc_searchview_description_clear = 0x7f0e0018
com.measure.ar:string/time_picker_pm = 0x7f0e0090
com.measure.ar:string/abc_search_hint = 0x7f0e0017
com.measure.ar:style/Widget.AppCompat.ActivityChooserView = 0x7f0f0121
com.measure.ar:string/abc_prepend_shortcut_label = 0x7f0e0016
com.measure.ar:string/abc_menu_sym_shortcut_label = 0x7f0e0015
com.measure.ar:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f001a
com.measure.ar:string/abc_menu_shift_shortcut_label = 0x7f0e0013
com.measure.ar:string/abc_menu_ctrl_shortcut_label = 0x7f0e000e
com.measure.ar:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0094
com.measure.ar:string/abc_capital_on = 0x7f0e000c
com.measure.ar:string/abc_capital_off = 0x7f0e000b
com.measure.ar:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0076
com.measure.ar:string/abc_activitychooserview_choose_application = 0x7f0e000a
com.measure.ar:string/abc_activity_chooser_view_see_all = 0x7f0e0009
com.measure.ar:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0048
com.measure.ar:string/abc_action_mode_done = 0x7f0e0008
com.measure.ar:string/abc_action_bar_up_description = 0x7f0e0006
com.measure.ar:string/abc_action_bar_home_description = 0x7f0e0005
com.measure.ar:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0153
com.measure.ar:string/__arcore_install_feature = 0x7f0e0003
com.measure.ar:string/__arcore_install_app = 0x7f0e0002
com.measure.ar:string/__arcore_cancel = 0x7f0e0000
com.measure.ar:mipmap/ic_launcher = 0x7f0c0000
com.measure.ar:layout/support_simple_spinner_dropdown_item = 0x7f0b0027
com.measure.ar:layout/select_dialog_multichoice_material = 0x7f0b0025
com.measure.ar:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0050
com.measure.ar:layout/select_dialog_item_material = 0x7f0b0024
com.measure.ar:layout/notification_template_part_time = 0x7f0b0023
com.measure.ar:layout/notification_action_tombstone = 0x7f0b001f
com.measure.ar:style/TextAppearance.AppCompat.Title = 0x7f0f00da
com.measure.ar:layout/notification_action = 0x7f0b001e
com.measure.ar:string/date_picker_scroll_to_later_years = 0x7f0e0045
com.measure.ar:layout/custom_dialog = 0x7f0b001d
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f0035
com.measure.ar:layout/abc_tooltip = 0x7f0b001c
com.measure.ar:layout/abc_screen_toolbar = 0x7f0b0018
com.measure.ar:string/abc_action_menu_overflow_description = 0x7f0e0007
com.measure.ar:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0017
com.measure.ar:layout/abc_screen_content_include = 0x7f0b0015
com.measure.ar:style/Widget.AppCompat.ListMenuView = 0x7f0f0148
com.measure.ar:layout/abc_popup_menu_header_item_layout = 0x7f0b0013
com.measure.ar:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0086
com.measure.ar:layout/abc_list_menu_item_radio = 0x7f0b0012
com.measure.ar:layout/abc_list_menu_item_layout = 0x7f0b0011
com.measure.ar:layout/abc_alert_dialog_title_material = 0x7f0b000b
com.measure.ar:layout/abc_alert_dialog_button_bar_material = 0x7f0b0009
com.measure.ar:layout/abc_activity_chooser_view = 0x7f0b0007
com.measure.ar:layout/abc_action_mode_close_item_material = 0x7f0b0006
com.measure.ar:layout/abc_action_mode_bar = 0x7f0b0005
com.measure.ar:layout/abc_action_menu_layout = 0x7f0b0004
com.measure.ar:layout/abc_action_menu_item_layout = 0x7f0b0003
com.measure.ar:layout/abc_action_bar_up_container = 0x7f0b0002
com.measure.ar:layout/abc_action_bar_title_item = 0x7f0b0001
com.measure.ar:layout/__arcore_education = 0x7f0b0000
com.measure.ar:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.measure.ar:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.measure.ar:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.measure.ar:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.measure.ar:integer/config_tooltipAnimTime = 0x7f090003
com.measure.ar:styleable/NavHost = 0x7f100027
com.measure.ar:id/wrapped_composition_tag = 0x7f0800c5
com.measure.ar:styleable/MenuItem = 0x7f100021
com.measure.ar:id/wrap_content = 0x7f0800c4
com.measure.ar:style/Widget.AppCompat.Light.ActionBar = 0x7f0f0132
com.measure.ar:id/visible_removing_fragment_view_tag = 0x7f0800c2
com.measure.ar:id/view_tree_view_model_store_owner = 0x7f0800c1
com.measure.ar:id/useLogo = 0x7f0800bd
com.measure.ar:string/bottom_sheet_collapse_description = 0x7f0e0027
com.measure.ar:id/topPanel = 0x7f0800b9
com.measure.ar:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f00b7
com.measure.ar:id/title_template = 0x7f0800b7
com.measure.ar:id/titleDividerNoCustom = 0x7f0800b6
com.measure.ar:styleable/Capability = 0x7f100012
com.measure.ar:id/textSpacerNoTitle = 0x7f0800b3
com.measure.ar:id/text2 = 0x7f0800b1
com.measure.ar:string/unit_ft = 0x7f0e0094
com.measure.ar:id/tag_window_insets_animation_callback = 0x7f0800af
com.measure.ar:id/tag_unhandled_key_listeners = 0x7f0800ae
com.measure.ar:id/tag_unhandled_key_event_manager = 0x7f0800ad
com.measure.ar:id/tag_transition_group = 0x7f0800ac
com.measure.ar:id/tag_state_description = 0x7f0800ab
com.measure.ar:id/tag_on_receive_content_listener = 0x7f0800a8
com.measure.ar:id/tag_accessibility_pane_title = 0x7f0800a6
com.measure.ar:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f008e
com.measure.ar:string/quality_fair = 0x7f0e0077
com.measure.ar:id/tabMode = 0x7f0800a2
com.measure.ar:id/submit_area = 0x7f0800a1
com.measure.ar:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0149
com.measure.ar:id/submenuarrow = 0x7f0800a0
com.measure.ar:id/src_over = 0x7f08009f
com.measure.ar:style/TextAppearance.AppCompat.Caption = 0x7f0f00c4
com.measure.ar:id/src_in = 0x7f08009e
com.measure.ar:id/src_atop = 0x7f08009d
com.measure.ar:styleable/NavGraphNavigator = 0x7f100026
com.measure.ar:style/Base.DialogWindowTitle.AppCompat = 0x7f0f000a
com.measure.ar:id/split_action_bar = 0x7f08009c
com.measure.ar:styleable/TextAppearance = 0x7f100033
com.measure.ar:id/spacer = 0x7f08009a
com.measure.ar:id/showTitle = 0x7f080099
com.measure.ar:id/shortcut = 0x7f080096
com.measure.ar:raw/keep_arcore = 0x7f0d0000
com.measure.ar:integer/abc_config_activityShortDur = 0x7f090001
com.measure.ar:id/search_voice_btn = 0x7f080094
com.measure.ar:id/search_src_text = 0x7f080093
com.measure.ar:id/search_mag_icon = 0x7f080091
com.measure.ar:id/search_go_btn = 0x7f080090
com.measure.ar:id/search_edit_frame = 0x7f08008f
com.measure.ar:id/search_badge = 0x7f08008b
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0134
com.measure.ar:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f00b5
com.measure.ar:id/scrollView = 0x7f08008a
com.measure.ar:string/on = 0x7f0e0072
com.measure.ar:id/scrollIndicatorUp = 0x7f080089
com.measure.ar:id/right_icon = 0x7f080085
com.measure.ar:id/radio = 0x7f080083
com.measure.ar:id/progress_horizontal = 0x7f080082
com.measure.ar:id/progress_circular = 0x7f080081
com.measure.ar:styleable/GradientColor = 0x7f10001b
com.measure.ar:id/pooling_container_listener_holder_tag = 0x7f080080
com.measure.ar:id/performance = 0x7f08007f
com.measure.ar:id/on = 0x7f08007d
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f00e4
com.measure.ar:id/off = 0x7f08007c
com.measure.ar:id/notification_main_column_container = 0x7f08007b
com.measure.ar:style/Base.ThemeOverlay.AppCompat = 0x7f0f004b
com.measure.ar:id/notification_main_column = 0x7f08007a
com.measure.ar:string/call_notification_answer_video_action = 0x7f0e002f
com.measure.ar:integer/abc_config_activityDefaultDur = 0x7f090000
com.measure.ar:id/normal = 0x7f080078
com.measure.ar:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f00a9
com.measure.ar:id/none = 0x7f080077
com.measure.ar:id/never = 0x7f080076
com.measure.ar:id/multiply = 0x7f080074
com.measure.ar:id/nav_controller_view_tag = 0x7f080075
com.measure.ar:id/message = 0x7f080072
com.measure.ar:id/list_item = 0x7f080071
com.measure.ar:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070000
com.measure.ar:id/italic = 0x7f08006d
com.measure.ar:color/notification_action_color_filter = 0x7f05004c
com.measure.ar:id/inspection_slot_table_set = 0x7f08006b
com.measure.ar:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.measure.ar:id/group_divider = 0x7f080062
com.measure.ar:attr/switchPadding = 0x7f0300ff
com.measure.ar:id/fragment_container_view_tag = 0x7f080061
com.measure.ar:attr/toolbarNavigationButtonStyle = 0x7f030123
com.measure.ar:id/contentPanel = 0x7f08004f
com.measure.ar:id/forever = 0x7f080060
com.measure.ar:id/fitStart = 0x7f08005f
com.measure.ar:string/abc_searchview_description_voice = 0x7f0e001c
com.measure.ar:id/fillStart = 0x7f08005c
com.measure.ar:attr/actionModeStyle = 0x7f03001d
com.measure.ar:id/fillCenter = 0x7f08005a
com.measure.ar:id/expanded_menu = 0x7f080059
com.measure.ar:styleable/PopupWindowBackgroundState = 0x7f10002b
com.measure.ar:id/expand_activities_button = 0x7f080058
com.measure.ar:id/edit_query = 0x7f080056
com.measure.ar:id/content = 0x7f08004e
com.measure.ar:id/top = 0x7f0800b8
com.measure.ar:id/async = 0x7f080041
com.measure.ar:id/tag_on_apply_window_listener = 0x7f0800a7
com.measure.ar:id/always = 0x7f08003f
com.measure.ar:attr/popUpToInclusive = 0x7f0300d2
com.measure.ar:id/alertTitle = 0x7f08003e
com.measure.ar:style/Theme.AppCompat.NoActionBar = 0x7f0f010d
com.measure.ar:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.measure.ar:id/activity_chooser_view_content = 0x7f08003c
com.measure.ar:id/action_text = 0x7f08003a
com.measure.ar:id/action_mode_bar_stub = 0x7f080038
com.measure.ar:id/action_mode_bar = 0x7f080037
com.measure.ar:id/unchecked = 0x7f0800ba
com.measure.ar:id/action_menu_divider = 0x7f080035
com.measure.ar:attr/searchViewStyle = 0x7f0300e6
com.measure.ar:id/action_image = 0x7f080034
com.measure.ar:drawable/abc_list_longpressed_holo = 0x7f070027
com.measure.ar:id/action_container = 0x7f080031
com.measure.ar:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f00b9
com.measure.ar:attr/windowNoTitle = 0x7f030138
com.measure.ar:id/accessibility_custom_action_8 = 0x7f080028
com.measure.ar:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070038
com.measure.ar:id/accessibility_custom_action_4 = 0x7f080024
com.measure.ar:attr/ratingBarStyleSmall = 0x7f0300e0
com.measure.ar:dimen/tooltip_precise_anchor_extra_offset = 0x7f060073
com.measure.ar:id/accessibility_custom_action_31 = 0x7f080023
com.measure.ar:id/time = 0x7f0800b4
com.measure.ar:attr/actionButtonStyle = 0x7f03000c
com.measure.ar:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001e
com.measure.ar:id/accessibility_custom_action_29 = 0x7f080020
com.measure.ar:id/accessibility_custom_action_24 = 0x7f08001b
com.measure.ar:id/buttonPanel = 0x7f080045
com.measure.ar:id/accessibility_custom_action_23 = 0x7f08001a
com.measure.ar:id/accessibility_custom_action_22 = 0x7f080019
com.measure.ar:string/date_picker_switch_to_next_month = 0x7f0e0049
com.measure.ar:id/accessibility_custom_action_21 = 0x7f080018
com.measure.ar:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f009d
com.measure.ar:attr/fontProviderCerts = 0x7f03008b
com.measure.ar:drawable/ic_call_decline_low = 0x7f07005c
com.measure.ar:id/accessibility_custom_action_2 = 0x7f080016
com.measure.ar:attr/firstBaselineToTopHeight = 0x7f030087
com.measure.ar:id/accessibility_custom_action_19 = 0x7f080015
com.measure.ar:id/accessibility_custom_action_18 = 0x7f080014
com.measure.ar:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0800bf
com.measure.ar:attr/arrowShaftLength = 0x7f03002e
com.measure.ar:attr/colorSwitchThumbNormal = 0x7f03005c
com.measure.ar:attr/maxButtonHeight = 0x7f0300bb
com.measure.ar:id/accessibility_custom_action_17 = 0x7f080013
com.measure.ar:id/accessibility_custom_action_13 = 0x7f08000f
com.measure.ar:color/accent_material_light = 0x7f05001a
com.measure.ar:id/accessibility_custom_action_12 = 0x7f08000e
com.measure.ar:id/accessibility_custom_action_0 = 0x7f08000a
com.measure.ar:id/accessibility_action_clickable_span = 0x7f080009
com.measure.ar:style/Base.TextAppearance.AppCompat.Small = 0x7f0f0021
com.measure.ar:string/date_range_input_title = 0x7f0e0050
com.measure.ar:attr/checkMarkTint = 0x7f03004a
com.measure.ar:id/compose_view_saveable_id_tag = 0x7f08004c
com.measure.ar:layout/abc_alert_dialog_material = 0x7f0b000a
com.measure.ar:attr/popUpToSaveState = 0x7f0300d3
com.measure.ar:id/checked = 0x7f080048
com.measure.ar:id/__arcore_messageText = 0x7f080008
com.measure.ar:id/__arcore_continueButton = 0x7f080007
com.measure.ar:id/SHIFT = 0x7f080004
com.measure.ar:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f015c
com.measure.ar:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f00bd
com.measure.ar:color/material_grey_850 = 0x7f050044
com.measure.ar:id/META = 0x7f080003
com.measure.ar:attr/titleMargins = 0x7f03011f
com.measure.ar:id/ALT = 0x7f080000
com.measure.ar:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0147
com.measure.ar:drawable/test_level_drawable = 0x7f07006c
com.measure.ar:attr/actionBarWidgetTheme = 0x7f03000b
com.measure.ar:drawable/notify_panel_notification_icon_bg = 0x7f07006b
com.measure.ar:style/Widget.AppCompat.ActionMode = 0x7f0f0120
com.measure.ar:string/navigation_menu = 0x7f0e006f
com.measure.ar:drawable/notification_tile_bg = 0x7f07006a
com.measure.ar:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f012b
com.measure.ar:drawable/notification_bg_normal_pressed = 0x7f070066
com.measure.ar:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0104
com.measure.ar:attr/alertDialogStyle = 0x7f030027
com.measure.ar:drawable/notification_bg_normal = 0x7f070065
com.measure.ar:drawable/notification_bg_low_pressed = 0x7f070064
com.measure.ar:drawable/ic_launcher_placeholder = 0x7f07005f
com.measure.ar:drawable/ic_launcher_background = 0x7f07005d
com.measure.ar:drawable/ic_call_answer_low = 0x7f070058
com.measure.ar:string/permission_camera_description = 0x7f0e0073
com.measure.ar:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070054
com.measure.ar:id/action_bar_root = 0x7f08002d
com.measure.ar:attr/fontFamily = 0x7f030089
com.measure.ar:attr/listPreferredItemPaddingRight = 0x7f0300b7
com.measure.ar:drawable/ic_call_answer_video = 0x7f070059
com.measure.ar:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f0061
com.measure.ar:string/date_picker_switch_to_year_selection = 0x7f0e004b
com.measure.ar:attr/controlBackground = 0x7f030065
com.measure.ar:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070050
com.measure.ar:drawable/abc_vector_test = 0x7f07004e
com.measure.ar:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07004b
com.measure.ar:styleable/LinearLayoutCompat = 0x7f10001d
com.measure.ar:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f012d
com.measure.ar:drawable/abc_textfield_default_mtrl_alpha = 0x7f07004a
com.measure.ar:style/Base.Widget.AppCompat.ImageButton = 0x7f0f0082
com.measure.ar:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.measure.ar:color/material_blue_grey_800 = 0x7f05003a
com.measure.ar:drawable/abc_text_select_handle_right_mtrl = 0x7f070048
com.measure.ar:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0156
com.measure.ar:string/date_picker_switch_to_previous_month = 0x7f0e004a
com.measure.ar:attr/titleTextAppearance = 0x7f030120
com.measure.ar:drawable/abc_text_select_handle_left_mtrl = 0x7f070046
com.measure.ar:drawable/abc_text_cursor_material = 0x7f070045
com.measure.ar:drawable/abc_switch_track_mtrl_alpha = 0x7f070042
com.measure.ar:drawable/abc_switch_thumb_material = 0x7f070041
com.measure.ar:attr/textColorSearchUrl = 0x7f03010d
com.measure.ar:drawable/abc_star_half_black_48dp = 0x7f070040
com.measure.ar:drawable/abc_star_black_48dp = 0x7f07003f
com.measure.ar:style/TextAppearance.AppCompat.Body2 = 0x7f0f00c2
com.measure.ar:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.measure.ar:id/action_context_bar = 0x7f080032
com.measure.ar:drawable/abc_spinner_textfield_background_material = 0x7f07003e
com.measure.ar:attr/expandActivityOverflowButtonDrawable = 0x7f030086
com.measure.ar:dimen/tooltip_y_offset_touch = 0x7f060077
com.measure.ar:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070039
com.measure.ar:drawable/abc_ratingbar_small_material = 0x7f070034
com.measure.ar:dimen/abc_button_inset_vertical_material = 0x7f060013
com.measure.ar:drawable/abc_popup_background_mtrl_mult = 0x7f070031
com.measure.ar:style/ThemeOverlay.AppCompat.Light = 0x7f0f0117
com.measure.ar:drawable/abc_list_selector_holo_dark = 0x7f07002e
com.measure.ar:drawable/abc_list_selector_background_transition_holo_light = 0x7f07002b
com.measure.ar:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f0103
com.measure.ar:id/image = 0x7f080069
com.measure.ar:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07002a
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0138
com.measure.ar:drawable/abc_list_focused_holo = 0x7f070026
com.measure.ar:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f00fb
com.measure.ar:drawable/abc_list_divider_material = 0x7f070024
com.measure.ar:drawable/abc_ic_voice_search_api_material = 0x7f070021
com.measure.ar:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f0040
com.measure.ar:id/customPanel = 0x7f080051
com.measure.ar:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0047
com.measure.ar:drawable/abc_ic_search_api_material = 0x7f070020
com.measure.ar:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.measure.ar:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f07001f
com.measure.ar:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001d
com.measure.ar:id/search_button = 0x7f08008d
com.measure.ar:drawable/abc_ic_menu_overflow_material = 0x7f07001c
com.measure.ar:attr/actionProviderClass = 0x7f030022
com.measure.ar:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001b
com.measure.ar:drawable/abc_ic_go_search_api_material = 0x7f070019
com.measure.ar:style/TextAppearance.Compat.Notification.Title = 0x7f0f00f4
com.measure.ar:style/TextAppearance.AppCompat.Menu = 0x7f0f00d3
com.measure.ar:drawable/abc_dialog_material_background = 0x7f070013
com.measure.ar:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f0080
com.measure.ar:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f006f
com.measure.ar:attr/listDividerAlertDialog = 0x7f0300ad
com.measure.ar:anim/abc_popup_exit = 0x7f010004
com.measure.ar:drawable/abc_cab_background_internal_bg = 0x7f07000f
com.measure.ar:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000e
com.measure.ar:color/abc_tint_edittext = 0x7f050015
com.measure.ar:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000d
com.measure.ar:style/Widget.AppCompat.RatingBar = 0x7f0f0152
com.measure.ar:style/Base.Widget.AppCompat.ListView = 0x7f0f008d
com.measure.ar:attr/numericModifiers = 0x7f0300c6
com.measure.ar:attr/switchMinWidth = 0x7f0300fe
com.measure.ar:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000c
com.measure.ar:color/bright_foreground_material_light = 0x7f05002b
com.measure.ar:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000b
com.measure.ar:dimen/abc_star_big = 0x7f06003b
com.measure.ar:id/line1 = 0x7f08006e
com.measure.ar:id/action_bar_spinner = 0x7f08002e
com.measure.ar:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0017
com.measure.ar:attr/nestedScrollViewStyle = 0x7f0300c4
com.measure.ar:drawable/abc_btn_colored_material = 0x7f070007
com.measure.ar:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070006
com.measure.ar:attr/windowActionBarOverlay = 0x7f030130
com.measure.ar:color/measure_blue_dark = 0x7f050047
com.measure.ar:color/foreground_material_dark = 0x7f050036
com.measure.ar:drawable/abc_btn_check_material_anim = 0x7f070004
com.measure.ar:drawable/abc_btn_check_material = 0x7f070003
com.measure.ar:drawable/abc_btn_borderless_material = 0x7f070002
com.measure.ar:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f007a
com.measure.ar:drawable/abc_action_bar_item_background_material = 0x7f070001
com.measure.ar:id/dialog_button = 0x7f080054
com.measure.ar:dimen/tooltip_y_offset_non_touch = 0x7f060076
com.measure.ar:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f0056
com.measure.ar:drawable/notification_bg = 0x7f070061
com.measure.ar:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f0010
com.measure.ar:id/tag_accessibility_clickable_spans = 0x7f0800a4
com.measure.ar:dimen/tooltip_vertical_padding = 0x7f060075
com.measure.ar:id/blocking = 0x7f080043
com.measure.ar:id/search_bar = 0x7f08008c
com.measure.ar:id/accessibility_custom_action_16 = 0x7f080012
com.measure.ar:style/Base.V7.Theme.AppCompat.Light = 0x7f0f0062
com.measure.ar:color/accent_material_dark = 0x7f050019
com.measure.ar:color/switch_thumb_normal_material_dark = 0x7f050064
com.measure.ar:dimen/tooltip_margin = 0x7f060072
com.measure.ar:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f0092
com.measure.ar:dimen/notification_top_pad_large_text = 0x7f06006f
com.measure.ar:string/date_picker_today_description = 0x7f0e004d
com.measure.ar:attr/backgroundTintMode = 0x7f030039
com.measure.ar:dimen/notification_top_pad = 0x7f06006e
com.measure.ar:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f00ba
com.measure.ar:attr/drawableEndCompat = 0x7f030074
com.measure.ar:drawable/abc_list_pressed_holo_light = 0x7f070029
com.measure.ar:id/collapseActionView = 0x7f08004a
com.measure.ar:attr/drawableBottomCompat = 0x7f030073
com.measure.ar:id/accessibility_custom_action_5 = 0x7f080025
com.measure.ar:string/close_sheet = 0x7f0e0036
com.measure.ar:dimen/notification_subtext_size = 0x7f06006d
com.measure.ar:style/Widget.Compat.NotificationActionContainer = 0x7f0f0161
com.measure.ar:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.measure.ar:attr/showAsAction = 0x7f0300eb
com.measure.ar:id/custom = 0x7f080050
com.measure.ar:dimen/notification_small_icon_background_padding = 0x7f06006b
com.measure.ar:dimen/notification_media_narrow_margin = 0x7f060068
com.measure.ar:dimen/notification_large_icon_height = 0x7f060065
com.measure.ar:color/button_material_light = 0x7f05002d
com.measure.ar:dimen/abc_star_small = 0x7f06003d
com.measure.ar:id/actions = 0x7f08003b
com.measure.ar:dimen/notification_content_margin_start = 0x7f060064
com.measure.ar:dimen/notification_action_text_size = 0x7f060062
com.measure.ar:drawable/abc_control_background_material = 0x7f070012
com.measure.ar:attr/autoSizeTextType = 0x7f030034
com.measure.ar:id/accessibility_custom_action_11 = 0x7f08000d
com.measure.ar:dimen/hint_alpha_material_dark = 0x7f06005d
com.measure.ar:string/bottom_sheet_expand_description = 0x7f0e002a
com.measure.ar:string/abc_searchview_description_search = 0x7f0e001a
com.measure.ar:id/accessibility_custom_action_3 = 0x7f080021
com.measure.ar:drawable/abc_ratingbar_material = 0x7f070033
com.measure.ar:dimen/highlight_alpha_material_light = 0x7f06005c
com.measure.ar:dimen/highlight_alpha_material_dark = 0x7f06005b
com.measure.ar:style/Base.Theme.AppCompat = 0x7f0f003c
com.measure.ar:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070016
com.measure.ar:id/parentPanel = 0x7f08007e
com.measure.ar:dimen/disabled_alpha_material_light = 0x7f060059
com.measure.ar:dimen/compat_control_corner_material = 0x7f060055
com.measure.ar:id/middle = 0x7f080073
com.measure.ar:id/icon = 0x7f080066
com.measure.ar:attr/radioButtonStyle = 0x7f0300dd
com.measure.ar:color/abc_tint_btn_checkable = 0x7f050013
com.measure.ar:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001a
com.measure.ar:dimen/compat_button_padding_vertical_material = 0x7f060054
com.measure.ar:id/CTRL = 0x7f080001
com.measure.ar:attr/actionModeCopyDrawable = 0x7f030015
com.measure.ar:dimen/compat_button_padding_horizontal_material = 0x7f060053
com.measure.ar:attr/actionBarTabStyle = 0x7f030008
com.measure.ar:dimen/compat_button_inset_vertical_material = 0x7f060052
com.measure.ar:dimen/compat_button_inset_horizontal_material = 0x7f060051
com.measure.ar:id/accessibility_custom_action_30 = 0x7f080022
com.measure.ar:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.measure.ar:string/measurement_path = 0x7f0e006d
com.measure.ar:dimen/abc_text_size_small_material = 0x7f06004c
com.measure.ar:id/scrollIndicatorDown = 0x7f080088
com.measure.ar:color/primary_text_default_material_dark = 0x7f050052
com.measure.ar:id/disableHome = 0x7f080055
com.measure.ar:dimen/abc_text_size_medium_material = 0x7f060049
com.measure.ar:dimen/compat_notification_large_icon_max_width = 0x7f060057
com.measure.ar:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f009b
com.measure.ar:dimen/abc_text_size_title_material = 0x7f06004f
com.measure.ar:color/abc_hint_foreground_material_dark = 0x7f050007
com.measure.ar:dimen/abc_text_size_display_4_material = 0x7f060046
com.measure.ar:dimen/abc_text_size_display_2_material = 0x7f060044
com.measure.ar:dimen/abc_text_size_display_1_material = 0x7f060043
com.measure.ar:dimen/notification_small_icon_size_as_large = 0x7f06006c
com.measure.ar:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f00f6
com.measure.ar:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f0051
com.measure.ar:dimen/abc_text_size_caption_material = 0x7f060042
com.measure.ar:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070056
com.measure.ar:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070035
com.measure.ar:dimen/abc_text_size_button_material = 0x7f060041
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0133
com.measure.ar:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.measure.ar:color/ar_overlay = 0x7f05001e
com.measure.ar:dimen/abc_text_size_body_1_material = 0x7f06003f
com.measure.ar:dimen/abc_switch_padding = 0x7f06003e
com.measure.ar:id/accessibility_custom_action_27 = 0x7f08001e
com.measure.ar:drawable/notification_bg_low_normal = 0x7f070063
com.measure.ar:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f0064
com.measure.ar:color/foreground_material_light = 0x7f050037
com.measure.ar:drawable/abc_spinner_mtrl_am_alpha = 0x7f07003d
com.measure.ar:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f0141
com.measure.ar:layout/abc_activity_chooser_view_list_item = 0x7f0b0008
com.measure.ar:dimen/abc_star_medium = 0x7f06003c
com.measure.ar:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0143
com.measure.ar:attr/isLightTheme = 0x7f0300a3
com.measure.ar:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.measure.ar:id/default_activity_button = 0x7f080053
com.measure.ar:dimen/abc_search_view_preferred_height = 0x7f060036
com.measure.ar:dimen/notification_action_icon_size = 0x7f060061
com.measure.ar:dimen/abc_progress_bar_height_material = 0x7f060035
com.measure.ar:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.measure.ar:id/accessibility_custom_action_14 = 0x7f080010
com.measure.ar:dimen/abc_list_item_height_small_material = 0x7f060032
com.measure.ar:id/notification_background = 0x7f080079
com.measure.ar:dimen/notification_large_icon_width = 0x7f060066
com.measure.ar:dimen/abc_list_item_height_large_material = 0x7f060030
com.measure.ar:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f00cc
com.measure.ar:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.measure.ar:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.measure.ar:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.measure.ar:style/Base.Theme.AppCompat.Light = 0x7f0f0043
com.measure.ar:dimen/abc_dialog_title_divider_material = 0x7f060026
com.measure.ar:dimen/abc_dialog_padding_material = 0x7f060024
com.measure.ar:id/showCustom = 0x7f080097
com.measure.ar:dimen/abc_dialog_min_width_minor = 0x7f060023
com.measure.ar:string/abc_menu_delete_shortcut_label = 0x7f0e000f
com.measure.ar:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070036
com.measure.ar:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.measure.ar:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.measure.ar:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.measure.ar:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.measure.ar:dimen/abc_control_inset_material = 0x7f060019
com.measure.ar:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.measure.ar:style/Base.V23.Theme.AppCompat = 0x7f0f0059
com.measure.ar:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070030
com.measure.ar:dimen/notification_main_column_padding_top = 0x7f060067
com.measure.ar:color/ripple_material_light = 0x7f05005b
com.measure.ar:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.measure.ar:style/TextAppearance.AppCompat = 0x7f0f00c0
com.measure.ar:string/dropdown_menu = 0x7f0e005a
com.measure.ar:drawable/abc_seekbar_track_material = 0x7f07003c
com.measure.ar:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f000b
com.measure.ar:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.measure.ar:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.measure.ar:dimen/abc_action_button_min_width_material = 0x7f06000e
com.measure.ar:styleable/Navigator = 0x7f100029
com.measure.ar:style/Base.Widget.AppCompat.ActionMode = 0x7f0f0070
com.measure.ar:dimen/abc_action_button_min_height_material = 0x7f06000d
com.measure.ar:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f00e7
com.measure.ar:id/title = 0x7f0800b5
com.measure.ar:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070049
com.measure.ar:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.measure.ar:dimen/highlight_alpha_material_colored = 0x7f06005a
com.measure.ar:attr/autoSizePresetSizes = 0x7f030032
com.measure.ar:animator/fragment_close_enter = 0x7f020000
com.measure.ar:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.measure.ar:string/date_picker_navigate_to_year_description = 0x7f0e0042
com.measure.ar:drawable/abc_list_divider_mtrl_alpha = 0x7f070025
com.measure.ar:color/switch_thumb_material_light = 0x7f050063
com.measure.ar:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.measure.ar:attr/trackTint = 0x7f030129
com.measure.ar:drawable/abc_btn_default_mtrl_shape = 0x7f070008
com.measure.ar:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.measure.ar:style/Base.V26.Theme.AppCompat.Light = 0x7f0f005c
com.measure.ar:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.measure.ar:string/collapsed = 0x7f0e0037
com.measure.ar:drawable/btn_checkbox_unchecked_mtrl = 0x7f070051
com.measure.ar:attr/actionModeCutDrawable = 0x7f030016
com.measure.ar:attr/launchSingleTop = 0x7f0300a7
com.measure.ar:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.measure.ar:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.measure.ar:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f011f
com.measure.ar:color/vector_tint_theme_color = 0x7f050069
com.measure.ar:string/measurement_volume = 0x7f0e006e
com.measure.ar:layout/notification_template_part_chronometer = 0x7f0b0022
com.measure.ar:dimen/disabled_alpha_material_dark = 0x7f060058
com.measure.ar:color/vector_tint_color = 0x7f050068
com.measure.ar:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0044
com.measure.ar:color/tooltip_background_dark = 0x7f050066
com.measure.ar:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.measure.ar:color/switch_thumb_normal_material_light = 0x7f050065
com.measure.ar:id/fillEnd = 0x7f08005b
com.measure.ar:id/action_menu_presenter = 0x7f080036
com.measure.ar:attr/actionBarSplitStyle = 0x7f030005
com.measure.ar:color/switch_thumb_material_dark = 0x7f050062
com.measure.ar:color/switch_thumb_disabled_material_dark = 0x7f050060
com.measure.ar:styleable/NavAction = 0x7f100023
com.measure.ar:color/secondary_text_disabled_material_light = 0x7f05005f
com.measure.ar:color/secondary_text_default_material_dark = 0x7f05005c
com.measure.ar:attr/textColorAlertDialogListItem = 0x7f03010c
com.measure.ar:id/accessibility_custom_action_10 = 0x7f08000c
com.measure.ar:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f007f
com.measure.ar:color/ripple_material_dark = 0x7f05005a
com.measure.ar:id/up = 0x7f0800bc
com.measure.ar:color/quality_fair = 0x7f050057
com.measure.ar:dimen/abc_text_size_large_material = 0x7f060048
com.measure.ar:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f011b
com.measure.ar:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f00b6
com.measure.ar:color/primary_text_disabled_material_light = 0x7f050055
com.measure.ar:attr/actionBarTabTextStyle = 0x7f030009
com.measure.ar:dimen/abc_dialog_padding_top_material = 0x7f060025
com.measure.ar:id/accessibility_custom_action_28 = 0x7f08001f
com.measure.ar:color/primary_text_disabled_material_dark = 0x7f050054
com.measure.ar:attr/progressBarStyle = 0x7f0300d9
com.measure.ar:dimen/abc_config_prefDialogWidth = 0x7f060017
com.measure.ar:color/primary_material_light = 0x7f050051
com.measure.ar:id/hide_in_inspector_tag = 0x7f080063
com.measure.ar:attr/windowFixedHeightMajor = 0x7f030132
com.measure.ar:id/end = 0x7f080057
com.measure.ar:color/notification_icon_bg_color = 0x7f05004d
com.measure.ar:attr/subtitleTextStyle = 0x7f0300fc
com.measure.ar:id/consume_window_insets_tag = 0x7f08004d
com.measure.ar:attr/autoSizeStepGranularity = 0x7f030033
com.measure.ar:drawable/ic_call_answer = 0x7f070057
com.measure.ar:drawable/abc_list_selector_holo_light = 0x7f07002f
com.measure.ar:attr/actionModeTheme = 0x7f03001e
com.measure.ar:color/measure_red = 0x7f05004b
com.measure.ar:attr/dialogPreferredPadding = 0x7f03006c
com.measure.ar:color/measure_orange = 0x7f05004a
com.measure.ar:color/measure_green = 0x7f050048
com.measure.ar:color/measure_blue = 0x7f050046
com.measure.ar:id/beginning = 0x7f080042
com.measure.ar:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f003d
com.measure.ar:id/text = 0x7f0800b0
com.measure.ar:color/material_grey_800 = 0x7f050043
com.measure.ar:id/withText = 0x7f0800c3
com.measure.ar:color/material_grey_50 = 0x7f050041
com.measure.ar:id/androidx_compose_ui_view_composition_context = 0x7f080040
com.measure.ar:color/material_grey_300 = 0x7f050040
com.measure.ar:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0019
com.measure.ar:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0137
com.measure.ar:color/material_deep_teal_500 = 0x7f05003e
com.measure.ar:color/material_deep_teal_200 = 0x7f05003d
com.measure.ar:string/abc_shareactionprovider_share_with = 0x7f0e001d
com.measure.ar:color/highlighted_text_material_light = 0x7f050039
com.measure.ar:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f0093
com.measure.ar:color/highlighted_text_material_dark = 0x7f050038
com.measure.ar:string/tab = 0x7f0e0083
com.measure.ar:drawable/ic_call_decline = 0x7f07005b
com.measure.ar:attr/drawableStartCompat = 0x7f030078
com.measure.ar:color/error_color_material_light = 0x7f050035
com.measure.ar:string/loading_calibration = 0x7f0e0068
com.measure.ar:color/error_color_material_dark = 0x7f050034
com.measure.ar:attr/exitAnim = 0x7f030085
com.measure.ar:color/dim_foreground_material_light = 0x7f050033
com.measure.ar:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070037
com.measure.ar:style/TextAppearance.Compat.Notification.Time = 0x7f0f00f3
com.measure.ar:style/Base.Widget.AppCompat.ActionButton = 0x7f0f006d
com.measure.ar:layout/abc_list_menu_item_icon = 0x7f0b0010
com.measure.ar:attr/thickness = 0x7f030110
com.measure.ar:color/button_material_dark = 0x7f05002c
com.measure.ar:color/black = 0x7f050025
com.measure.ar:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0116
com.measure.ar:color/background_material_dark = 0x7f050023
com.measure.ar:color/ar_measure_line = 0x7f05001d
com.measure.ar:attr/textAppearanceListItem = 0x7f030105
com.measure.ar:attr/homeAsUpIndicator = 0x7f030099
com.measure.ar:string/expanded = 0x7f0e005e
com.measure.ar:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.measure.ar:attr/popupMenuStyle = 0x7f0300d4
com.measure.ar:id/checkbox = 0x7f080047
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f013a
com.measure.ar:id/icon_group = 0x7f080067
com.measure.ar:color/abc_tint_switch_track = 0x7f050018
com.measure.ar:layout/abc_screen_simple = 0x7f0b0016
com.measure.ar:color/call_notification_answer_color = 0x7f05002e
com.measure.ar:attr/titleTextStyle = 0x7f030122
com.measure.ar:drawable/abc_list_selector_disabled_holo_dark = 0x7f07002c
com.measure.ar:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0075
com.measure.ar:color/androidx_core_ripple_material_light = 0x7f05001b
com.measure.ar:dimen/abc_list_item_height_material = 0x7f060031
com.measure.ar:string/date_picker_scroll_to_earlier_years = 0x7f0e0044
com.measure.ar:attr/colorControlHighlight = 0x7f030057
com.measure.ar:color/bright_foreground_disabled_material_dark = 0x7f050026
com.measure.ar:id/fitEnd = 0x7f08005e
com.measure.ar:string/tooltip_long_press_label = 0x7f0e0091
com.measure.ar:color/abc_secondary_text_material_light = 0x7f050012
com.measure.ar:styleable/AppCompatTheme = 0x7f100010
com.measure.ar:string/instruction_add_corner = 0x7f0e0061
com.measure.ar:color/abc_secondary_text_material_dark = 0x7f050011
com.measure.ar:style/Theme.AppCompat.DayNight = 0x7f0f00fa
com.measure.ar:attr/colorPrimary = 0x7f03005a
com.measure.ar:color/abc_search_url_text_selected = 0x7f050010
com.measure.ar:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f0026
com.measure.ar:id/action_bar_subtitle = 0x7f08002f
com.measure.ar:attr/popupTheme = 0x7f0300d5
com.measure.ar:attr/fontProviderAuthority = 0x7f03008a
com.measure.ar:color/abc_tint_spinner = 0x7f050017
com.measure.ar:layout/abc_search_view = 0x7f0b001a
com.measure.ar:color/abc_search_url_text = 0x7f05000d
com.measure.ar:color/abc_primary_text_material_light = 0x7f05000c
com.measure.ar:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f007c
com.measure.ar:color/bright_foreground_inverse_material_light = 0x7f050029
com.measure.ar:id/tag_accessibility_actions = 0x7f0800a3
com.measure.ar:attr/fontProviderQuery = 0x7f03008f
com.measure.ar:attr/buttonBarNegativeButtonStyle = 0x7f03003d
com.measure.ar:animator/fragment_open_exit = 0x7f020005
com.measure.ar:style/ThemeOverlay.AppCompat.Dark = 0x7f0f0111
com.measure.ar:attr/argType = 0x7f03002c
com.measure.ar:color/abc_decor_view_status_guard = 0x7f050005
com.measure.ar:attr/color = 0x7f030052
com.measure.ar:attr/titleMarginBottom = 0x7f03011b
com.measure.ar:string/abc_menu_meta_shortcut_label = 0x7f0e0012
com.measure.ar:id/home = 0x7f080064
com.measure.ar:color/abc_btn_colored_text_material = 0x7f050003
com.measure.ar:string/tooltip_pane_description = 0x7f0e0092
com.measure.ar:string/date_range_picker_scroll_to_previous_month = 0x7f0e0054
com.measure.ar:attr/collapseIcon = 0x7f030051
com.measure.ar:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.measure.ar:color/measure_green_dark = 0x7f050049
com.measure.ar:styleable/StateListDrawable = 0x7f100030
com.measure.ar:attr/preserveIconSpacing = 0x7f0300d7
com.measure.ar:attr/barLength = 0x7f03003a
com.measure.ar:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f011a
com.measure.ar:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.measure.ar:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0088
com.measure.ar:attr/subtitleTextColor = 0x7f0300fb
com.measure.ar:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.measure.ar:color/tooltip_background_light = 0x7f050067
com.measure.ar:attr/state_above_anchor = 0x7f0300f6
com.measure.ar:color/abc_primary_text_material_dark = 0x7f05000b
com.measure.ar:id/SYM = 0x7f080005
com.measure.ar:attr/actionModeFindDrawable = 0x7f030017
com.measure.ar:attr/textAppearanceListItemSecondary = 0x7f030106
com.measure.ar:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f0032
com.measure.ar:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070018
com.measure.ar:attr/popUpTo = 0x7f0300d1
com.measure.ar:string/calibration_instruction = 0x7f0e002c
com.measure.ar:attr/windowFixedWidthMajor = 0x7f030134
com.measure.ar:string/quality_good = 0x7f0e0078
com.measure.ar:dimen/hint_alpha_material_light = 0x7f06005e
com.measure.ar:attr/windowFixedHeightMinor = 0x7f030133
com.measure.ar:color/material_blue_grey_900 = 0x7f05003b
com.measure.ar:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f007b
com.measure.ar:attr/closeIcon = 0x7f03004e
com.measure.ar:bool/abc_action_bar_embed_tabs = 0x7f040000
com.measure.ar:id/screen = 0x7f080087
com.measure.ar:attr/submitBackground = 0x7f0300f8
com.measure.ar:attr/windowActionModeOverlay = 0x7f030131
com.measure.ar:attr/voiceIcon = 0x7f03012e
com.measure.ar:attr/actionModePasteDrawable = 0x7f030018
com.measure.ar:attr/switchTextAppearance = 0x7f030101
com.measure.ar:attr/enterAnim = 0x7f030084
com.measure.ar:attr/viewInflaterClass = 0x7f03012d
com.measure.ar:attr/uri = 0x7f03012c
com.measure.ar:attr/contentInsetStartWithNavigation = 0x7f030064
com.measure.ar:drawable/abc_item_background_holo_dark = 0x7f070022
com.measure.ar:dimen/abc_action_bar_default_height_material = 0x7f060002
com.measure.ar:attr/trackTintMode = 0x7f03012a
com.measure.ar:dimen/tooltip_corner_radius = 0x7f060070
com.measure.ar:attr/showTitle = 0x7f0300ee
com.measure.ar:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f000e
com.measure.ar:attr/actionBarItemBackground = 0x7f030002
com.measure.ar:color/bright_foreground_inverse_material_dark = 0x7f050028
com.measure.ar:attr/track = 0x7f030128
com.measure.ar:attr/multiChoiceItemLayout = 0x7f0300bf
com.measure.ar:attr/listChoiceIndicatorMultipleAnimated = 0x7f0300ab
com.measure.ar:attr/tooltipText = 0x7f030127
com.measure.ar:attr/iconTintMode = 0x7f03009d
com.measure.ar:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.measure.ar:style/Widget.AppCompat.SearchView = 0x7f0f0155
com.measure.ar:color/background_material_light = 0x7f050024
com.measure.ar:color/quality_poor = 0x7f050059
com.measure.ar:attr/tooltipForegroundColor = 0x7f030125
com.measure.ar:styleable/FontFamilyFont = 0x7f100018
com.measure.ar:attr/toolbarStyle = 0x7f030124
com.measure.ar:id/accessibility_custom_action_9 = 0x7f080029
com.measure.ar:attr/titleTextColor = 0x7f030121
com.measure.ar:layout/abc_popup_menu_item_layout = 0x7f0b0014
com.measure.ar:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.measure.ar:color/abc_tint_seek_thumb = 0x7f050016
com.measure.ar:attr/popEnterAnim = 0x7f0300cf
com.measure.ar:color/abc_hint_foreground_material_light = 0x7f050008
com.measure.ar:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f001c
com.measure.ar:attr/titleMarginTop = 0x7f03011e
com.measure.ar:attr/titleMarginStart = 0x7f03011d
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0135
com.measure.ar:drawable/abc_text_select_handle_middle_mtrl = 0x7f070047
com.measure.ar:attr/destination = 0x7f03006a
com.measure.ar:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0136
com.measure.ar:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.measure.ar:attr/buttonTintMode = 0x7f030048
com.measure.ar:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f012c
com.measure.ar:attr/titleMarginEnd = 0x7f03011c
com.measure.ar:id/right_side = 0x7f080086
com.measure.ar:attr/editTextBackground = 0x7f03007f
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0036
com.measure.ar:attr/listLayout = 0x7f0300af
com.measure.ar:color/bright_foreground_material_dark = 0x7f05002a
com.measure.ar:styleable/ActionBarLayout = 0x7f100001
com.measure.ar:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.measure.ar:id/action_bar_title = 0x7f080030
com.measure.ar:id/accessibility_custom_action_15 = 0x7f080011
com.measure.ar:string/in_progress = 0x7f0e005f
com.measure.ar:color/material_blue_grey_950 = 0x7f05003c
com.measure.ar:attr/title = 0x7f030119
com.measure.ar:string/m3c_bottom_sheet_pane_title = 0x7f0e0069
com.measure.ar:attr/tintMode = 0x7f030118
com.measure.ar:color/material_grey_600 = 0x7f050042
com.measure.ar:drawable/abc_btn_radio_material = 0x7f070009
com.measure.ar:attr/tickMarkTintMode = 0x7f030116
com.measure.ar:color/secondary_text_default_material_light = 0x7f05005d
com.measure.ar:attr/textAppearanceLargePopupMenu = 0x7f030104
com.measure.ar:attr/textLocale = 0x7f03010e
com.measure.ar:drawable/abc_cab_background_top_material = 0x7f070010
com.measure.ar:attr/textAppearanceSearchResultTitle = 0x7f03010a
com.measure.ar:attr/actionLayout = 0x7f03000e
com.measure.ar:style/AlertDialog.AppCompat = 0x7f0f0000
com.measure.ar:attr/textAppearanceSearchResultSubtitle = 0x7f030109
com.measure.ar:anim/abc_tooltip_exit = 0x7f01000b
com.measure.ar:attr/textAppearanceListItemSmall = 0x7f030107
com.measure.ar:attr/thumbTextPadding = 0x7f030111
com.measure.ar:id/view_tree_lifecycle_owner = 0x7f0800be
com.measure.ar:attr/colorError = 0x7f030059
com.measure.ar:attr/switchStyle = 0x7f030100
com.measure.ar:styleable/SearchView = 0x7f10002e
com.measure.ar:dimen/abc_text_size_body_2_material = 0x7f060040
com.measure.ar:dimen/abc_text_size_display_3_material = 0x7f060045
com.measure.ar:attr/startDestination = 0x7f0300f5
com.measure.ar:attr/tint = 0x7f030117
com.measure.ar:drawable/tooltip_frame_light = 0x7f07006e
com.measure.ar:attr/splitTrack = 0x7f0300f3
com.measure.ar:drawable/notification_bg_low = 0x7f070062
com.measure.ar:string/suggestions_available = 0x7f0e0081
com.measure.ar:attr/singleChoiceItemLayout = 0x7f0300ef
com.measure.ar:style/Base.AlertDialog.AppCompat = 0x7f0f0005
com.measure.ar:attr/selectableItemBackground = 0x7f0300e8
com.measure.ar:dimen/abc_action_bar_elevation_material = 0x7f060005
com.measure.ar:attr/actionMenuTextColor = 0x7f030010
com.measure.ar:id/accessibility_custom_action_7 = 0x7f080027
com.measure.ar:attr/spinBars = 0x7f0300f0
com.measure.ar:attr/seekBarStyle = 0x7f0300e7
com.measure.ar:attr/searchIcon = 0x7f0300e5
com.measure.ar:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.measure.ar:attr/scaleType = 0x7f0300e3
com.measure.ar:style/AlertDialog.AppCompat.Light = 0x7f0f0001
com.measure.ar:attr/actionOverflowButtonStyle = 0x7f030020
com.measure.ar:style/Widget.AppCompat.EditText = 0x7f0f0130
com.measure.ar:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f00cf
com.measure.ar:attr/restoreState = 0x7f0300e1
com.measure.ar:attr/windowMinWidthMinor = 0x7f030137
com.measure.ar:attr/dropDownListViewStyle = 0x7f03007d
com.measure.ar:string/date_input_label = 0x7f0e003d
com.measure.ar:string/__arcore_installing = 0x7f0e0004
com.measure.ar:attr/drawableRightCompat = 0x7f030076
com.measure.ar:attr/ratingBarStyleIndicator = 0x7f0300df
com.measure.ar:attr/actionOverflowMenuStyle = 0x7f030021
com.measure.ar:color/dim_foreground_disabled_material_light = 0x7f050031
com.measure.ar:dimen/hint_pressed_alpha_material_dark = 0x7f06005f
com.measure.ar:attr/queryBackground = 0x7f0300da
com.measure.ar:attr/checkboxStyle = 0x7f03004c
com.measure.ar:attr/panelBackground = 0x7f0300cc
com.measure.ar:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f008b
com.measure.ar:attr/popupWindowStyle = 0x7f0300d6
com.measure.ar:attr/buttonBarPositiveButtonStyle = 0x7f03003f
com.measure.ar:dimen/notification_big_circle_margin = 0x7f060063
com.measure.ar:attr/actionModeSelectAllDrawable = 0x7f03001a
com.measure.ar:attr/homeLayout = 0x7f03009a
com.measure.ar:attr/displayOptions = 0x7f03006e
com.measure.ar:attr/tickMark = 0x7f030114
com.measure.ar:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.measure.ar:anim/abc_slide_out_bottom = 0x7f010008
com.measure.ar:string/default_error_message = 0x7f0e0057
com.measure.ar:attr/suggestionRowLayout = 0x7f0300fd
com.measure.ar:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0109
com.measure.ar:anim/fragment_fast_out_extra_slow_in = 0x7f010018
com.measure.ar:attr/actionDropDownStyle = 0x7f03000d
com.measure.ar:attr/popExitAnim = 0x7f0300d0
com.measure.ar:id/chronometer = 0x7f080049
com.measure.ar:attr/alertDialogButtonGroupStyle = 0x7f030025
com.measure.ar:id/search_plate = 0x7f080092
com.measure.ar:attr/thumbTintMode = 0x7f030113
com.measure.ar:attr/paddingTopNoTitle = 0x7f0300cb
com.measure.ar:styleable/ActivityChooserView = 0x7f100005
com.measure.ar:attr/subtitleTextAppearance = 0x7f0300fa
com.measure.ar:attr/paddingStart = 0x7f0300ca
com.measure.ar:attr/paddingEnd = 0x7f0300c9
com.measure.ar:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070044
com.measure.ar:attr/dropdownListPreferredItemHeight = 0x7f03007e
com.measure.ar:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f009f
com.measure.ar:attr/paddingBottomNoButtons = 0x7f0300c8
com.measure.ar:style/Base.Theme.Measure = 0x7f0f004a
com.measure.ar:layout/select_dialog_singlechoice_material = 0x7f0b0026
com.measure.ar:color/background_floating_material_dark = 0x7f050021
com.measure.ar:style/TextAppearance.AppCompat.Tooltip = 0x7f0f00dc
com.measure.ar:id/bottom = 0x7f080044
com.measure.ar:attr/theme = 0x7f03010f
com.measure.ar:attr/activityChooserViewStyle = 0x7f030024
com.measure.ar:attr/overlapAnchor = 0x7f0300c7
com.measure.ar:drawable/ic_call_answer_video_low = 0x7f07005a
com.measure.ar:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f00b3
com.measure.ar:mipmap/ic_launcher_round = 0x7f0c0001
com.measure.ar:attr/nullable = 0x7f0300c5
com.measure.ar:id/center_vertical = 0x7f080046
com.measure.ar:drawable/ic_launcher_foreground = 0x7f07005e
com.measure.ar:attr/navGraph = 0x7f0300c0
com.measure.ar:layout/abc_dialog_title_material = 0x7f0b000d
com.measure.ar:attr/actionModeCloseDrawable = 0x7f030014
com.measure.ar:drawable/notification_icon_background = 0x7f070067
com.measure.ar:style/Widget.AppCompat.ListView.Menu = 0x7f0f014c
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f002c
com.measure.ar:drawable/abc_edit_text_material = 0x7f070014
com.measure.ar:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0154
com.measure.ar:string/abc_toolbar_collapse_description = 0x7f0e001f
com.measure.ar:string/abc_menu_space_shortcut_label = 0x7f0e0014
com.measure.ar:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.measure.ar:string/date_range_picker_title = 0x7f0e0056
com.measure.ar:layout/abc_list_menu_item_checkbox = 0x7f0b000f
com.measure.ar:attr/menu = 0x7f0300bd
com.measure.ar:dimen/abc_text_size_menu_material = 0x7f06004b
com.measure.ar:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f0016
com.measure.ar:color/abc_decor_view_status_guard_light = 0x7f050006
com.measure.ar:string/range_start = 0x7f0e007b
com.measure.ar:attr/textAppearancePopupMenuHeader = 0x7f030108
com.measure.ar:attr/measureWithLargestChild = 0x7f0300bc
com.measure.ar:attr/listPreferredItemHeightSmall = 0x7f0300b4
com.measure.ar:attr/logo = 0x7f0300b9
com.measure.ar:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070052
com.measure.ar:attr/thumbTint = 0x7f030112
com.measure.ar:dimen/abc_control_padding_material = 0x7f06001a
com.measure.ar:attr/queryHint = 0x7f0300db
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f002d
com.measure.ar:attr/listPreferredItemPaddingStart = 0x7f0300b8
com.measure.ar:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0125
com.measure.ar:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.measure.ar:attr/fontProviderSystemFontFamily = 0x7f030090
com.measure.ar:color/call_notification_decline_color = 0x7f05002f
com.measure.ar:attr/queryPatterns = 0x7f0300dc
com.measure.ar:attr/listPreferredItemPaddingEnd = 0x7f0300b5
com.measure.ar:drawable/abc_list_pressed_holo_dark = 0x7f070028
com.measure.ar:style/Widget.AppCompat.TextView = 0x7f0f015d
com.measure.ar:attr/shortcutMatchRequired = 0x7f0300ea
com.measure.ar:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f008f
com.measure.ar:id/action_divider = 0x7f080033
com.measure.ar:id/ifRoom = 0x7f080068
com.measure.ar:dimen/abc_control_corner_material = 0x7f060018
com.measure.ar:attr/listPreferredItemHeightLarge = 0x7f0300b3
com.measure.ar:style/Platform.V21.AppCompat = 0x7f0f00aa
com.measure.ar:color/quality_excellent = 0x7f050056
com.measure.ar:style/Widget.AppCompat.Toolbar = 0x7f0f015f
com.measure.ar:style/Widget.AppCompat.PopupMenu = 0x7f0f014d
com.measure.ar:attr/listPreferredItemHeight = 0x7f0300b2
com.measure.ar:id/tag_on_receive_content_mime_types = 0x7f0800a9
com.measure.ar:attr/listPopupWindowStyle = 0x7f0300b1
com.measure.ar:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f0107
com.measure.ar:id/action_mode_close_button = 0x7f080039
com.measure.ar:attr/listMenuViewStyle = 0x7f0300b0
com.measure.ar:attr/listItemLayout = 0x7f0300ae
com.measure.ar:styleable/AppCompatTextHelper = 0x7f10000e
com.measure.ar:attr/buttonBarStyle = 0x7f030040
com.measure.ar:interpolator/fast_out_slow_in = 0x7f0a0006
com.measure.ar:attr/listChoiceIndicatorSingleAnimated = 0x7f0300ac
com.measure.ar:string/range_end = 0x7f0e007a
com.measure.ar:attr/lineHeight = 0x7f0300a9
com.measure.ar:styleable/LinearLayoutCompat_Layout = 0x7f10001e
com.measure.ar:attr/layout = 0x7f0300a8
com.measure.ar:string/unit_in = 0x7f0e0095
com.measure.ar:layout/abc_cascading_menu_item_layout = 0x7f0b000c
com.measure.ar:attr/lastBaselineToBottomHeight = 0x7f0300a6
com.measure.ar:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f0140
com.measure.ar:id/info = 0x7f08006a
com.measure.ar:style/Base.Widget.AppCompat.Toolbar = 0x7f0f00a0
com.measure.ar:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.measure.ar:attr/autoSizeMinTextSize = 0x7f030031
com.measure.ar:attr/iconifiedByDefault = 0x7f03009e
com.measure.ar:styleable/MenuGroup = 0x7f100020
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0030
com.measure.ar:color/white = 0x7f05006a
com.measure.ar:attr/itemPadding = 0x7f0300a4
com.measure.ar:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.measure.ar:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.measure.ar:id/special_effects_controller_view_tag = 0x7f08009b
com.measure.ar:color/background_floating_material_light = 0x7f050022
com.measure.ar:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f07004c
com.measure.ar:color/primary_dark_material_light = 0x7f05004f
com.measure.ar:style/Widget.AppCompat.ButtonBar = 0x7f0f0129
com.measure.ar:attr/initialActivityCount = 0x7f0300a2
com.measure.ar:style/Theme.AppCompat.CompactMenu = 0x7f0f00f9
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f00de
com.measure.ar:attr/indeterminateProgressStyle = 0x7f0300a1
com.measure.ar:attr/implementationMode = 0x7f0300a0
com.measure.ar:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00bf
com.measure.ar:attr/imageButtonStyle = 0x7f03009f
com.measure.ar:drawable/btn_radio_on_mtrl = 0x7f070055
com.measure.ar:color/dim_foreground_material_dark = 0x7f050032
com.measure.ar:attr/searchHintIcon = 0x7f0300e4
com.measure.ar:attr/background = 0x7f030035
com.measure.ar:attr/iconTint = 0x7f03009c
com.measure.ar:attr/icon = 0x7f03009b
com.measure.ar:drawable/abc_ic_clear_material = 0x7f070017
com.measure.ar:string/indeterminate = 0x7f0e0060
com.measure.ar:attr/navigationMode = 0x7f0300c3
com.measure.ar:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f00d7
com.measure.ar:attr/drawableSize = 0x7f030077
com.measure.ar:styleable/AppCompatTextView = 0x7f10000f
com.measure.ar:attr/buttonBarNeutralButtonStyle = 0x7f03003e
com.measure.ar:attr/graph = 0x7f030096
com.measure.ar:attr/fontWeight = 0x7f030093
com.measure.ar:attr/logoDescription = 0x7f0300ba
com.measure.ar:attr/contentInsetEndWithActions = 0x7f030060
com.measure.ar:styleable/DrawerArrowToggle = 0x7f100016
com.measure.ar:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.measure.ar:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f012f
com.measure.ar:attr/dividerHorizontal = 0x7f030070
com.measure.ar:attr/buttonIconDimen = 0x7f030043
com.measure.ar:attr/fontProviderPackage = 0x7f03008e
com.measure.ar:attr/panelMenuListWidth = 0x7f0300ce
com.measure.ar:attr/spinnerDropDownItemStyle = 0x7f0300f1
com.measure.ar:anim/abc_slide_in_bottom = 0x7f010006
com.measure.ar:attr/height = 0x7f030097
com.measure.ar:style/Base.Widget.AppCompat.TextView = 0x7f0f009e
com.measure.ar:attr/fontProviderFetchStrategy = 0x7f03008c
com.measure.ar:attr/fontStyle = 0x7f030091
com.measure.ar:attr/spinnerStyle = 0x7f0300f2
com.measure.ar:dimen/compat_notification_large_icon_max_height = 0x7f060056
com.measure.ar:attr/buttonStyle = 0x7f030045
com.measure.ar:attr/checkMarkCompat = 0x7f030049
com.measure.ar:styleable/ActivityNavigator = 0x7f100006
com.measure.ar:attr/actionModeBackground = 0x7f030011
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f002a
com.measure.ar:attr/colorButtonNormal = 0x7f030055
com.measure.ar:attr/progressBarPadding = 0x7f0300d8
com.measure.ar:layout/abc_select_dialog_material = 0x7f0b001b
com.measure.ar:dimen/abc_button_padding_vertical_material = 0x7f060015
com.measure.ar:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0029
com.measure.ar:attr/actionMenuTextAppearance = 0x7f03000f
com.measure.ar:attr/elevation = 0x7f030082
com.measure.ar:attr/fontProviderFetchTimeout = 0x7f03008d
com.measure.ar:string/abc_searchview_description_query = 0x7f0e0019
com.measure.ar:attr/actionViewClass = 0x7f030023
com.measure.ar:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f00af
com.measure.ar:attr/allowStacking = 0x7f030029
com.measure.ar:style/Base.V28.Theme.AppCompat = 0x7f0f005e
com.measure.ar:id/report_drawn = 0x7f080084
com.measure.ar:color/material_grey_100 = 0x7f05003f
com.measure.ar:attr/buttonPanelSideLayout = 0x7f030044
com.measure.ar:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f00ff
com.measure.ar:style/Base.AlertDialog.AppCompat.Light = 0x7f0f0006
com.measure.ar:string/action_calibrate = 0x7f0e0020
com.measure.ar:drawable/notification_action_background = 0x7f070060
com.measure.ar:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f00ce
com.measure.ar:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0084
com.measure.ar:attr/drawerArrowStyle = 0x7f03007c
com.measure.ar:attr/buttonTint = 0x7f030047
com.measure.ar:id/accessibility_custom_action_1 = 0x7f08000b
com.measure.ar:attr/drawableTopCompat = 0x7f03007b
com.measure.ar:id/decor_content_parent = 0x7f080052
com.measure.ar:drawable/abc_textfield_search_material = 0x7f07004d
com.measure.ar:attr/dividerVertical = 0x7f030072
com.measure.ar:integer/status_bar_notification_info_maxnum = 0x7f090004
com.measure.ar:attr/dividerPadding = 0x7f030071
com.measure.ar:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.measure.ar:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f00a8
com.measure.ar:style/Base.Animation.AppCompat.Dialog = 0x7f0f0007
com.measure.ar:attr/commitIcon = 0x7f03005d
com.measure.ar:style/TextAppearance.AppCompat.Subhead = 0x7f0f00d8
com.measure.ar:drawable/abc_ic_ab_back_material = 0x7f070015
com.measure.ar:attr/subMenuArrow = 0x7f0300f7
com.measure.ar:string/measurement_line = 0x7f0e006c
com.measure.ar:attr/buttonCompat = 0x7f030041
com.measure.ar:attr/dialogCornerRadius = 0x7f03006b
com.measure.ar:attr/customNavigationLayout = 0x7f030066
com.measure.ar:style/Platform.V25.AppCompat = 0x7f0f00ac
com.measure.ar:color/bright_foreground_disabled_material_light = 0x7f050027
com.measure.ar:color/secondary_text_disabled_material_dark = 0x7f05005e
com.measure.ar:attr/editTextStyle = 0x7f030081
com.measure.ar:style/TextAppearance.Compat.Notification.Info = 0x7f0f00f1
com.measure.ar:layout/abc_expanded_menu_layout = 0x7f0b000e
com.measure.ar:attr/subtitle = 0x7f0300f9
com.measure.ar:attr/tickMarkTint = 0x7f030115
com.measure.ar:attr/ratingBarStyle = 0x7f0300de
com.measure.ar:dimen/abc_floating_window_z = 0x7f06002f
com.measure.ar:string/instruction_tap_second_point = 0x7f0e0066
com.measure.ar:attr/contentInsetRight = 0x7f030062
com.measure.ar:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f00e1
com.measure.ar:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f006b
com.measure.ar:dimen/abc_dialog_min_width_major = 0x7f060022
com.measure.ar:attr/listPreferredItemPaddingLeft = 0x7f0300b6
com.measure.ar:id/action_bar_activity_content = 0x7f08002b
com.measure.ar:attr/contentInsetStart = 0x7f030063
com.measure.ar:drawable/abc_item_background_holo_light = 0x7f070023
com.measure.ar:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.measure.ar:attr/backgroundTint = 0x7f030038
com.measure.ar:styleable/StateListDrawableItem = 0x7f100031
com.measure.ar:id/accessibility_custom_action_25 = 0x7f08001c
com.measure.ar:id/FUNCTION = 0x7f080002
com.measure.ar:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0151
com.measure.ar:attr/contentInsetLeft = 0x7f030061
com.measure.ar:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f0096
com.measure.ar:attr/dataPattern = 0x7f030068
com.measure.ar:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.measure.ar:attr/windowActionBar = 0x7f03012f
com.measure.ar:attr/contentInsetEnd = 0x7f03005f
com.measure.ar:id/uniform = 0x7f0800bb
com.measure.ar:id/is_pooling_container_tag = 0x7f08006c
com.measure.ar:drawable/abc_seekbar_tick_mark_material = 0x7f07003b
com.measure.ar:id/textSpacerNoButtons = 0x7f0800b2
com.measure.ar:attr/fontVariationSettings = 0x7f030092
com.measure.ar:style/ThemeOverlay.AppCompat = 0x7f0f010f
com.measure.ar:color/quality_good = 0x7f050058
com.measure.ar:string/abc_menu_function_shortcut_label = 0x7f0e0011
com.measure.ar:string/abc_menu_alt_shortcut_label = 0x7f0e000d
com.measure.ar:anim/abc_slide_in_top = 0x7f010007
com.measure.ar:id/search_close_btn = 0x7f08008e
com.measure.ar:color/dim_foreground_disabled_material_dark = 0x7f050030
com.measure.ar:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.measure.ar:style/Base.V21.Theme.AppCompat.Light = 0x7f0f0054
com.measure.ar:attr/colorControlNormal = 0x7f030058
com.measure.ar:id/fitCenter = 0x7f08005d
com.measure.ar:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f014e
com.measure.ar:attr/showDividers = 0x7f0300ec
com.measure.ar:attr/backgroundSplit = 0x7f030036
com.measure.ar:attr/srcCompat = 0x7f0300f4
com.measure.ar:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f0122
com.measure.ar:attr/colorBackgroundFloating = 0x7f030054
com.measure.ar:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0028
com.measure.ar:color/ar_reticle = 0x7f05001f
com.measure.ar:style/Widget.AppCompat.Button.Borderless = 0x7f0f0124
com.measure.ar:attr/collapseContentDescription = 0x7f030050
com.measure.ar:id/accessibility_custom_action_26 = 0x7f08001d
com.measure.ar:attr/closeItemLayout = 0x7f03004f
com.measure.ar:color/material_grey_900 = 0x7f050045
com.measure.ar:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f015a
com.measure.ar:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f00ee
com.measure.ar:attr/panelMenuListTheme = 0x7f0300cd
com.measure.ar:attr/showText = 0x7f0300ed
com.measure.ar:color/abc_tint_default = 0x7f050014
com.measure.ar:attr/dialogTheme = 0x7f03006d
com.measure.ar:id/select_dialog_listview = 0x7f080095
com.measure.ar:attr/buttonGravity = 0x7f030042
com.measure.ar:attr/lStar = 0x7f0300a5
com.measure.ar:id/compatible = 0x7f08004b
com.measure.ar:attr/ttcIndex = 0x7f03012b
com.measure.ar:dimen/tooltip_horizontal_padding = 0x7f060071
com.measure.ar:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f0142
com.measure.ar:attr/drawableTint = 0x7f030079
com.measure.ar:integer/cancel_button_image_alpha = 0x7f090002
com.measure.ar:attr/colorAccent = 0x7f030053
com.measure.ar:styleable/NavInclude = 0x7f100028
com.measure.ar:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.measure.ar:id/view_tree_saved_state_registry_owner = 0x7f0800c0
com.measure.ar:color/primary_dark_material_dark = 0x7f05004e
com.measure.ar:attr/drawableTintMode = 0x7f03007a
com.measure.ar:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070011
com.measure.ar:style/Theme.AppCompat.Dialog = 0x7f0f0101
com.measure.ar:attr/actionModeCloseContentDescription = 0x7f030013
com.measure.ar:attr/textAllCaps = 0x7f030103
com.measure.ar:attr/action = 0x7f030000
com.measure.ar:drawable/abc_ratingbar_indicator_material = 0x7f070032
com.measure.ar:id/tag_screen_reader_focusable = 0x7f0800aa
com.measure.ar:attr/buttonBarButtonStyle = 0x7f03003c
com.measure.ar:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f00ef
com.measure.ar:attr/borderlessButtonStyle = 0x7f03003b
com.measure.ar:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.measure.ar:color/abc_color_highlight_material = 0x7f050004
com.measure.ar:attr/actionBarTabBarStyle = 0x7f030007
com.measure.ar:attr/tooltipFrameBackground = 0x7f030126
com.measure.ar:attr/backgroundStacked = 0x7f030037
com.measure.ar:string/call_notification_incoming_text = 0x7f0e0032
com.measure.ar:attr/titleMargin = 0x7f03011a
com.measure.ar:attr/contentDescription = 0x7f03005e
com.measure.ar:attr/goIcon = 0x7f030095
com.measure.ar:attr/selectableItemBackgroundBorderless = 0x7f0300e9
com.measure.ar:attr/actionModeCloseButtonStyle = 0x7f030012
com.measure.ar:string/not_selected = 0x7f0e0070
com.measure.ar:attr/actionBarSize = 0x7f030004
com.measure.ar:style/Base.Widget.AppCompat.EditText = 0x7f0f0081
com.measure.ar:animator/fragment_open_enter = 0x7f020004
com.measure.ar:style/FloatingDialogWindowTheme = 0x7f0f00a4
com.measure.ar:color/abc_search_url_text_pressed = 0x7f05000f
com.measure.ar:attr/autoSizeMaxTextSize = 0x7f030030
com.measure.ar:id/accessibility_custom_action_6 = 0x7f080026
com.measure.ar:id/action_bar_container = 0x7f08002c
com.measure.ar:animator/fragment_close_exit = 0x7f020001
com.measure.ar:styleable/AppCompatImageView = 0x7f10000c
com.measure.ar:drawable/notification_template_icon_low_bg = 0x7f070069
com.measure.ar:style/Base.V28.Theme.AppCompat.Light = 0x7f0f005f
com.measure.ar:attr/colorPrimaryDark = 0x7f03005b
com.measure.ar:style/Widget.AppCompat.Button.Colored = 0x7f0f0127
com.measure.ar:dimen/tooltip_precise_anchor_threshold = 0x7f060074
com.measure.ar:string/status_bar_notification_info_overflow = 0x7f0e0080
com.measure.ar:attr/alphabeticModifiers = 0x7f03002b
com.measure.ar:string/abc_shareactionprovider_share_with_application = 0x7f0e001e
com.measure.ar:attr/listChoiceBackgroundIndicator = 0x7f0300aa
com.measure.ar:dimen/abc_search_view_preferred_width = 0x7f060037
com.measure.ar:color/switch_thumb_disabled_material_light = 0x7f050061
com.measure.ar:anim/abc_tooltip_enter = 0x7f01000a
com.measure.ar:id/listMode = 0x7f080070
com.measure.ar:drawable/notification_template_icon_bg = 0x7f070068
com.measure.ar:attr/gapBetweenBars = 0x7f030094
com.measure.ar:attr/actionBarStyle = 0x7f030006
com.measure.ar:styleable/ActionMenuItemView = 0x7f100002
com.measure.ar:attr/actionModePopupWindowStyle = 0x7f030019
com.measure.ar:drawable/abc_tab_indicator_material = 0x7f070043
com.measure.ar:attr/alertDialogCenterButtons = 0x7f030026
com.measure.ar:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0039
com.measure.ar:dimen/abc_text_size_headline_material = 0x7f060047
com.measure.ar:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00a1
com.measure.ar:color/abc_search_url_text_normal = 0x7f05000e
com.measure.ar:styleable/ListPopupWindow = 0x7f10001f
com.measure.ar:attr/actionModeWebSearchDrawable = 0x7f03001f
com.measure.ar:dimen/notification_right_icon_size = 0x7f060069
com.measure.ar:string/bottom_sheet_dismiss_description = 0x7f0e0028
com.measure.ar:id/homeAsUp = 0x7f080065
com.measure.ar:attr/hideOnContentScroll = 0x7f030098
com.measure.ar:attr/defaultQueryHint = 0x7f030069
com.measure.ar:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.measure.ar:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.measure.ar:attr/windowFixedWidthMinor = 0x7f030135
com.measure.ar:attr/route = 0x7f0300e2
com.measure.ar:style/Platform.Widget.AppCompat.Spinner = 0x7f0f00ae
com.measure.ar:attr/autoCompleteTextViewStyle = 0x7f03002f
com.measure.ar:attr/mimeType = 0x7f0300be
com.measure.ar:attr/actionModeSplitBackground = 0x7f03001c
com.measure.ar:attr/actionModeShareDrawable = 0x7f03001b
com.measure.ar:drawable/abc_list_selector_disabled_holo_light = 0x7f07002d
com.measure.ar:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f00b8
com.measure.ar:attr/checkedTextViewStyle = 0x7f03004d
com.measure.ar:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f0055
com.measure.ar:id/line3 = 0x7f08006f
com.measure.ar:color/primary_material_dark = 0x7f050050
com.measure.ar:anim/abc_fade_in = 0x7f010000
com.measure.ar:attr/arrowHeadLength = 0x7f03002d
com.measure.ar:styleable/View = 0x7f100035
com.measure.ar:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.measure.ar:style/TextAppearance.AppCompat.Medium = 0x7f0f00d1
com.measure.ar:id/tag_accessibility_heading = 0x7f0800a5
com.measure.ar:attr/drawableLeftCompat = 0x7f030075
com.measure.ar:attr/colorControlActivated = 0x7f030056
com.measure.ar:anim/abc_fade_out = 0x7f010001
com.measure.ar:drawable/btn_checkbox_checked_mtrl = 0x7f07004f
com.measure.ar:attr/checkMarkTintMode = 0x7f03004b
com.measure.ar:styleable/Spinner = 0x7f10002f
com.measure.ar:attr/targetPackage = 0x7f030102
com.measure.ar:id/accessibility_custom_action_20 = 0x7f080017
com.measure.ar:animator/fragment_fade_enter = 0x7f020002
com.measure.ar:attr/font = 0x7f030088
com.measure.ar:attr/alertDialogTheme = 0x7f030028
com.measure.ar:drawable/tooltip_frame_dark = 0x7f07006d
com.measure.ar:dimen/hint_pressed_alpha_material_light = 0x7f060060
com.measure.ar:attr/buttonStyleSmall = 0x7f030046
com.measure.ar:attr/editTextColor = 0x7f030080
com.measure.ar:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f0024
com.measure.ar:dimen/abc_text_size_subhead_material = 0x7f06004d
com.measure.ar:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.measure.ar:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f004e
com.measure.ar:attr/windowMinWidthMajor = 0x7f030136
com.measure.ar:attr/divider = 0x7f03006f
com.measure.ar:animator/fragment_fade_exit = 0x7f020003
com.measure.ar:attr/alpha = 0x7f03002a
com.measure.ar:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f008a
com.measure.ar:drawable/abc_seekbar_thumb_material = 0x7f07003a
com.measure.ar:attr/actionBarPopupTheme = 0x7f030003
com.measure.ar:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f0113
com.measure.ar:drawable/abc_btn_radio_material_anim = 0x7f07000a
com.measure.ar:style/Theme.AppCompat = 0x7f0f00f8
com.measure.ar:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f004c
com.measure.ar:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.measure.ar:color/primary_text_default_material_light = 0x7f050053
com.measure.ar:string/date_range_input_invalid_range_input = 0x7f0e004f
com.measure.ar:string/__arcore_continue = 0x7f0e0001
com.measure.ar:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070005
com.measure.ar:style/Widget.AppCompat.Button.Small = 0x7f0f0128
com.measure.ar:attr/navigationContentDescription = 0x7f0300c1
com.measure.ar:style/Base.Widget.AppCompat.SearchView = 0x7f0f0098
com.measure.ar:id/action_bar = 0x7f08002a
com.measure.ar:style/Widget.AppCompat.ActionButton = 0x7f0f011d
com.measure.ar:string/abc_menu_enter_shortcut_label = 0x7f0e0010
com.measure.ar:attr/actionBarTheme = 0x7f03000a
com.measure.ar:anim/abc_popup_enter = 0x7f010003
com.measure.ar:layout/notification_template_icon_group = 0x7f0b0021
com.measure.ar:id/add = 0x7f08003d
com.measure.ar:id/showHome = 0x7f080098
com.measure.ar:attr/navigationIcon = 0x7f0300c2
com.measure.ar:anim/abc_slide_out_top = 0x7f010009
com.measure.ar:dimen/abc_panel_menu_list_width = 0x7f060034
com.measure.ar:color/ar_snap_indicator = 0x7f050020
com.measure.ar:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f00fd
com.measure.ar:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.measure.ar:styleable/ColorStateListItem = 0x7f100014
com.measure.ar:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f0110
com.measure.ar:id/__arcore_cancelButton = 0x7f080006
com.measure.ar:attr/data = 0x7f030067
com.measure.ar:layout/notification_template_custom_big = 0x7f0b0020
com.measure.ar:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.measure.ar:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f0074
com.measure.ar:attr/emojiCompatEnabled = 0x7f030083
com.measure.ar:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f00d4
com.measure.ar:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f006e
com.measure.ar:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.measure.ar:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f00db
com.measure.ar:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f0014
com.measure.ar:dimen/notification_right_side_padding_top = 0x7f06006a
com.measure.ar:style/Platform.V21.AppCompat.Light = 0x7f0f00ab
com.measure.ar:attr/actionBarDivider = 0x7f030001
com.measure.ar:drawable/btn_radio_off_mtrl = 0x7f070053
com.measure.ar:string/date_input_invalid_year_range = 0x7f0e003c
com.measure.ar:attr/textAppearanceSmallPopupMenu = 0x7f03010b
