# Measure AR App - Project Verification Script
# This script verifies the project structure and provides setup guidance

Write-Host "=== Measure AR App - Project Verification ===" -ForegroundColor Cyan
Write-Host ""

# Check project structure
Write-Host "1. Checking project structure..." -ForegroundColor Yellow

$requiredFiles = @(
    "build.gradle",
    "settings.gradle", 
    "gradle.properties",
    "app\build.gradle",
    "app\src\main\AndroidManifest.xml",
    "app\src\main\java\com\measure\ar\MainActivity.kt"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $file" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -eq 0) {
    Write-Host "  Project structure is complete!" -ForegroundColor Green
} else {
    Write-Host "  Missing files detected. Please check the project setup." -ForegroundColor Red
}

Write-Host ""

# Check for Java/JDK
Write-Host "2. Checking Java installation..." -ForegroundColor Yellow

try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✓ Java is installed" -ForegroundColor Green
        Write-Host "    Version: $($javaVersion[0])" -ForegroundColor Gray
    } else {
        throw "Java not found"
    }
} catch {
    Write-Host "  ✗ Java/JDK not found" -ForegroundColor Red
    Write-Host "    Please install JDK 11+ from https://adoptium.net/" -ForegroundColor Yellow
}

Write-Host ""

# Check for Android Studio
Write-Host "3. Checking Android development environment..." -ForegroundColor Yellow

$androidStudioPaths = @(
    "${env:ProgramFiles}\Android\Android Studio",
    "${env:ProgramFiles(x86)}\Android\Android Studio",
    "${env:LOCALAPPDATA}\Android\Sdk"
)

$androidStudioFound = $false
foreach ($path in $androidStudioPaths) {
    if (Test-Path $path) {
        Write-Host "  ✓ Android development tools found at: $path" -ForegroundColor Green
        $androidStudioFound = $true
        break
    }
}

if (-not $androidStudioFound) {
    Write-Host "  ✗ Android Studio not found" -ForegroundColor Red
    Write-Host "    Please install from https://developer.android.com/studio" -ForegroundColor Yellow
}

Write-Host ""

# Check documentation
Write-Host "4. Checking documentation..." -ForegroundColor Yellow

$docFiles = @(
    "README.md",
    "SETUP_GUIDE.md", 
    "docs\TECHNICAL_SPECIFICATION.md",
    "docs\UX_DESIGN_SPECIFICATION.md",
    "docs\TESTING_PLAN.md",
    "docs\PROJECT_SUMMARY.md"
)

foreach ($doc in $docFiles) {
    if (Test-Path $doc) {
        Write-Host "  ✓ $doc" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $doc" -ForegroundColor Red
    }
}

Write-Host ""

# Project statistics
Write-Host "5. Project Statistics..." -ForegroundColor Yellow

$kotlinFiles = Get-ChildItem -Path "app\src" -Filter "*.kt" -Recurse -ErrorAction SilentlyContinue
$xmlFiles = Get-ChildItem -Path "app\src" -Filter "*.xml" -Recurse -ErrorAction SilentlyContinue
$totalLines = 0

if ($kotlinFiles) {
    foreach ($file in $kotlinFiles) {
        $lines = (Get-Content $file.FullName -ErrorAction SilentlyContinue).Count
        $totalLines += $lines
    }
    Write-Host "  Kotlin files: $($kotlinFiles.Count)" -ForegroundColor Green
}

if ($xmlFiles) {
    Write-Host "  XML files: $($xmlFiles.Count)" -ForegroundColor Green
}

Write-Host "  Total lines of code: $totalLines" -ForegroundColor Green

Write-Host ""

# Next steps
Write-Host "=== Next Steps ===" -ForegroundColor Cyan

if ($missingFiles.Count -eq 0) {
    Write-Host "✓ Project structure is ready!" -ForegroundColor Green
    
    if ((Get-Command java -ErrorAction SilentlyContinue) -and $androidStudioFound) {
        Write-Host "✓ Development environment is ready!" -ForegroundColor Green
        Write-Host ""
        Write-Host "You can now:" -ForegroundColor White
        Write-Host "  1. Open the project in Android Studio" -ForegroundColor Gray
        Write-Host "  2. Sync Gradle dependencies" -ForegroundColor Gray
        Write-Host "  3. Run on an ARCore-compatible device" -ForegroundColor Gray
        Write-Host "  4. Start developing and testing!" -ForegroundColor Gray
    } else {
        Write-Host "⚠ Development environment needs setup" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Please complete the setup:" -ForegroundColor White
        Write-Host "  1. Install JDK 11+ if not already installed" -ForegroundColor Gray
        Write-Host "  2. Install Android Studio" -ForegroundColor Gray
        Write-Host "  3. Follow the SETUP_GUIDE.md for detailed instructions" -ForegroundColor Gray
    }
} else {
    Write-Host "⚠ Project setup incomplete" -ForegroundColor Yellow
    Write-Host "Please check the missing files and project structure." -ForegroundColor Gray
}

Write-Host ""
Write-Host "📖 For detailed setup instructions, see: SETUP_GUIDE.md" -ForegroundColor Cyan
Write-Host "📚 For technical details, see: docs\TECHNICAL_SPECIFICATION.md" -ForegroundColor Cyan
Write-Host ""

# Pause to let user read the output
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
