[{"merged": "com.measure.ar.app-merged_res-58:/drawable_ic_launcher_background.xml.flat", "source": "com.measure.ar.app-main-60:/drawable/ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.measure.ar.app-merged_res-58:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.measure.ar.app-main-60:\\xml\\data_extraction_rules.xml"}, {"merged": "com.measure.ar.app-merged_res-58:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.measure.ar.app-main-60:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.measure.ar.app-merged_res-58:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.measure.ar.app-main-60:\\xml\\backup_rules.xml"}, {"merged": "com.measure.ar.app-merged_res-58:/drawable_ic_launcher_placeholder.xml.flat", "source": "com.measure.ar.app-main-60:/drawable/ic_launcher_placeholder.xml"}, {"merged": "com.measure.ar.app-merged_res-58:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.measure.ar.app-main-60:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.measure.ar.app-merged_res-58:/drawable_ic_launcher_foreground.xml.flat", "source": "com.measure.ar.app-main-60:/drawable/ic_launcher_foreground.xml"}]