package com.measure.ar

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import com.measure.ar.ui.screens.MeasureScreen
import com.measure.ar.ui.theme.MeasureTheme
import com.measure.ar.viewmodel.MeasureViewModel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            MeasureTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    MeasureApp(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun MeasureApp(
    modifier: Modifier = Modifier,
    viewModel: MeasureViewModel = viewModel()
) {
    MeasureScreen(
        viewModel = viewModel,
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun MeasureAppPreview() {
    MeasureTheme {
        MeasureApp()
    }
}
