# Setup Guide - Measure AR App

## Prerequisites Installation

### 1. Install Java Development Kit (JDK)
The Android project requires JDK 11 or higher.

**Option A: Install OpenJDK (Recommended)**
1. Download OpenJDK 17 from: https://adoptium.net/
2. Run the installer and follow the setup wizard
3. Verify installation: Open Command Prompt and run `java -version`

**Option B: Install Oracle JDK**
1. Download from: https://www.oracle.com/java/technologies/downloads/
2. Install and set JAVA_HOME environment variable

### 2. Install Android Studio
1. Download Android Studio from: https://developer.android.com/studio
2. Run the installer and follow the setup wizard
3. During setup, ensure these components are installed:
   - Android SDK
   - Android SDK Platform-Tools
   - Android Virtual Device (AVD)
   - Intel x86 Emulator Accelerator (if using Intel processor)

### 3. Configure Android SDK
1. Open Android Studio
2. Go to File → Settings → Appearance & Behavior → System Settings → Android SDK
3. Install the following:
   - Android 14 (API level 34) - Target SDK
   - Android 7.0 (API level 24) - Minimum SDK
   - Android SDK Build-Tools 34.0.0
   - Google Play services
   - Google Repository

## Project Setup

### 1. Open Project in Android Studio
1. Launch Android Studio
2. Select "Open an existing project"
3. Navigate to the "Measure Claude" folder
4. Click "OK" to open the project

### 2. Sync Project
1. Android Studio will automatically prompt to sync the project
2. Click "Sync Now" when prompted
3. Wait for the sync to complete (may take several minutes on first run)

### 3. Configure ARCore
1. Ensure your device supports ARCore: https://developers.google.com/ar/devices
2. Install ARCore on your device from Google Play Store

## Device Setup for Testing

### Physical Device (Recommended)
1. **Enable Developer Options:**
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings → Developer Options

2. **Enable USB Debugging:**
   - In Developer Options, enable "USB Debugging"
   - Connect device via USB
   - Accept the debugging prompt on your device

3. **Install ARCore:**
   - Install "Google Play Services for AR" from Play Store
   - Verify ARCore is working by opening any AR app

### Emulator Setup (Limited AR Support)
1. In Android Studio, go to Tools → AVD Manager
2. Create a new Virtual Device
3. Select a device with API 29+ and Google Play
4. Note: AR features will be limited in emulator

## Building the Project

### Using Android Studio
1. Open the project in Android Studio
2. Wait for Gradle sync to complete
3. Click the "Run" button (green triangle) or press Shift+F10
4. Select your target device
5. The app will build and install automatically

### Using Command Line (After JDK Installation)
```bash
# Navigate to project directory
cd "C:\Users\<USER>\OneDrive\Desktop\Measure Claude"

# Build the project
.\gradlew build

# Install on connected device
.\gradlew installDebug

# Run tests
.\gradlew test
```

## Troubleshooting

### Common Issues

**1. "SDK location not found"**
- Solution: Set ANDROID_HOME environment variable to your SDK path
- Typical path: `C:\Users\<USER>\AppData\Local\Android\Sdk`

**2. "Java version incompatible"**
- Solution: Ensure JDK 11+ is installed and JAVA_HOME is set correctly

**3. "ARCore not supported"**
- Solution: Test on ARCore-compatible device or use ARCore emulator

**4. "Gradle sync failed"**
- Solution: Check internet connection and try "File → Invalidate Caches and Restart"

**5. "Build tools version not found"**
- Solution: Install required build tools via SDK Manager

### Performance Optimization
1. **Increase Gradle memory:**
   - Add to `gradle.properties`: `org.gradle.jvmargs=-Xmx4g`

2. **Enable parallel builds:**
   - Add to `gradle.properties`: `org.gradle.parallel=true`

3. **Use Gradle daemon:**
   - Add to `gradle.properties`: `org.gradle.daemon=true`

## Testing the App

### Initial Verification
1. **Permissions Test:**
   - Launch app
   - Grant camera permission when prompted
   - Verify AR camera view appears

2. **Basic Measurement:**
   - Point camera at flat surface
   - Wait for plane detection (white dots)
   - Tap the "+" button to open measurement tools
   - Select "Line" measurement
   - Tap two points to measure distance

3. **Calibration Test:**
   - Tap "Calibrate" in the tool menu
   - Follow the figure-8 motion instructions
   - Verify calibration completes successfully

### Advanced Testing
1. **Accuracy Validation:**
   - Measure known objects (ruler, book, etc.)
   - Compare with actual measurements
   - Test in different lighting conditions

2. **Performance Testing:**
   - Monitor frame rate during measurement
   - Test on different devices
   - Verify smooth animations and interactions

## Development Workflow

### Making Changes
1. **Code Changes:**
   - Edit files in Android Studio
   - Use hot reload for UI changes (Compose)
   - Full rebuild for structural changes

2. **Testing Changes:**
   - Run unit tests: `.\gradlew test`
   - Run on device for integration testing
   - Use Android Studio debugger for troubleshooting

3. **Version Control:**
   - Commit changes regularly
   - Use meaningful commit messages
   - Create branches for new features

### Debugging
1. **Logcat:** View real-time logs in Android Studio
2. **Breakpoints:** Set breakpoints in code for step-through debugging
3. **AR Debugging:** Use ARCore debugging tools for AR-specific issues

## Next Steps

Once the setup is complete:

1. **Explore the Code:**
   - Review `MainActivity.kt` for app entry point
   - Examine `AREngine.kt` for AR functionality
   - Study `MeasureViewModel.kt` for business logic

2. **Customize Features:**
   - Modify measurement algorithms in `MathUtils.kt`
   - Adjust UI in Compose components
   - Add new measurement types

3. **Enhance Accuracy:**
   - Implement ML edge detection
   - Fine-tune sensor fusion algorithms
   - Add environmental calibration

4. **Deploy:**
   - Generate signed APK for distribution
   - Test on multiple devices
   - Prepare for Play Store submission

## Support Resources

- **Android Developer Documentation:** https://developer.android.com/
- **ARCore Documentation:** https://developers.google.com/ar/
- **Jetpack Compose Guide:** https://developer.android.com/jetpack/compose
- **Kotlin Documentation:** https://kotlinlang.org/docs/

For project-specific questions, refer to the comprehensive documentation in the `docs/` folder.
