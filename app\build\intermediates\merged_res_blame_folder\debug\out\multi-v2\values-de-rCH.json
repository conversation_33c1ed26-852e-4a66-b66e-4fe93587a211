{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-de-rCH/values-de-rCH.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-de-rCH\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "194,244,293,427,564", "endColumns": "49,48,133,136,90", "endOffsets": "243,292,426,563,654"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,109,162,300,441", "endColumns": "53,52,137,140,94", "endOffsets": "104,157,295,436,531"}}]}]}