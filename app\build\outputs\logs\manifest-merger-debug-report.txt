-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\43e6569ac27e856965cfce6eec3f10af\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4afb199f8169321ff870be33d1a94c7d\transformed\navigation-common-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\72b4e606400e8fbe6689466b853a46c7\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0d7db3122fa77f45ef09003065bf7e8\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c8de9522c5601c5c4191942ee422185\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\acbdce7833fa3726cbbd9257a9ca9cfd\transformed\navigation-compose-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\dad28f5405e8ecd83a477663f8c93b32\transformed\material3-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43d3c5043fe024bcdb18099973601d18\transformed\material-ripple-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8a76693bcf85482c2907b9bb520aee8a\transformed\material-icons-core-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\783b1771d7d4fa7637c00d77f327f6f7\transformed\material-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\247b15fb3656b83390c338c9acf24a5d\transformed\animation-core-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\990b427b3b7c13ccce1212e67eb42f60\transformed\animation-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2e2013cd2a1ebee7c237b884a99e710e\transformed\foundation-layout-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\080af24b86e7a736fb1d2c984e82b7d9\transformed\foundation-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\e227eadf7be79548f6e8e78f88680e43\transformed\ui-tooling-data-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\d909281ac61eae8c7e7efb504f9cf765\transformed\ui-unit-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\5f837dc2e580a9121c71e88ce1bd7515\transformed\ui-geometry-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\9cf4d73608557c13a2334b7b5e07448a\transformed\ui-util-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\3b7fbc2d3b16ce5f21357a1f8a807645\transformed\ui-text-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\090eebd3d32e73be9b29a435a3806f19\transformed\ui-tooling-preview-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\fa313ba462e989ede190af542803e7b9\transformed\ui-graphics-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\0680b79097ef8fc5aeaed9adf5e353a2\transformed\camera-view-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dd1b3a9f23fc52e969f73e7935c2a62\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\63bd5e984072d7829c453ef1d8a9d80b\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\6657ccc8b5733031a445c86dd277f1e3\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\3bfbc17fc8a77550b40416dafcf83e54\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9634d3098cf4280fd9535641350d796\transformed\activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2f933205bace544606e07716f0ee6247\transformed\ui-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d625ef4a8e1c099c507118cf27c90eab\transformed\camera-lifecycle-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e2d12991baf79c8e1f44a4805ba7ca42\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\389b0422173fdf5426d5b04ab3b53b5a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f438c4d62abd4ef0e7fa7a9171f3a99\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\49a20fe668bf08b4f0874cc61af9ae3c\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\267a358f6732eaa66a728f3aae7ef582\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\480c28e510a3eb34ebe93f33b9eb1b6a\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6004c5fc0ef70de5dbc64d4996f27a7b\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8cac778c5b5014b48464f1fb9ec0e5f\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\89826be8eb5db12a93d8900fb913d71c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\76536efdb59467b2f42d891a4d02cccf\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1581537c4bd667c24120fae23f8ecf3f\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b016eae976ad658305d804badf89321b\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6b206a5be7699569a0f8417f3efeaf2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\057092178933cdb1b322af828244aca3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88400a7129850269a5e50ed9dbb8d5e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\93bdf84ab7205201c0b1c9343d2dd603\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\081b130133ac2b62a3c0e4dea88a268d\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\19e4030d9138be6659621bb8f10fc4d8\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71dec1ee4cd0b03b81f745c0cb6bda4e\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4eaaabcb6df0d2483ee61c2c7a7d10e6\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\5adb8eea175570bf13de2e33f0d35e70\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\af82cc9f5e3603381a170f2db6b05b1c\transformed\runtime-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:8:1-40:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a044d2b3f6638ed43054eec441fbceb5\transformed\tensorflow-lite-support-0.4.3\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b463632e2d8961bbfc544acd065497f\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\9100ae86a1381429f4bf31308028bf90\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0fafc20f315e4de42cf14ab8ac94f56\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cab3b499e334996126f4f47c78f8f4f5\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\959f993d5ff15c8535eb5290d4983785\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54e19ededdf485f17df3fdff51655f8f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2001b411a6caddb6ab9573e39eb00977\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7107afd79c504ad9b646039c49df771a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\949176be70d371c5bc7d1a8796ab5a91\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce6ec2b410ccee7ec8662ccb55001e36\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1c3c22c5dd481e17badb3db1c2fce852\transformed\tensorflow-lite-support-api-0.4.3\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\2939904060c386b8b41d61590198f493\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\9ef5760ac40652b661f92ec1d777ca5a\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:7:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:11:5-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:11:22-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:15:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:16:22-77
uses-feature#android.hardware.camera.ar
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:5-87
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:61-84
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:19-60
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:5-98
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:72-95
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:19-71
uses-feature#android.hardware.sensor.gyroscope
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:5-94
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:68-91
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:19-67
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:24:5-50:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:24:5-50:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:24:5-38:19
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:24:5-38:19
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b463632e2d8961bbfc544acd065497f\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b463632e2d8961bbfc544acd065497f\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\9100ae86a1381429f4bf31308028bf90\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\9100ae86a1381429f4bf31308028bf90\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2001b411a6caddb6ab9573e39eb00977\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2001b411a6caddb6ab9573e39eb00977\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\2939904060c386b8b41d61590198f493\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\2939904060c386b8b41d61590198f493\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\9ef5760ac40652b661f92ec1d777ca5a\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\9ef5760ac40652b661f92ec1d777ca5a\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:31:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:29:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:27:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:30:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:33:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:28:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:25:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:32:9-45
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:26:9-65
meta-data#com.google.ar.core
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:9-81
	android:value
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:54-78
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:20-53
activity#com.measure.ar.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:38:9-49:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:43:13-49
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:41:13-45
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:40:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:44:13-74
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:42:13-49
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:39:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:45:13-48:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:46:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:46:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:47:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:47:27-74
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\43e6569ac27e856965cfce6eec3f10af\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\43e6569ac27e856965cfce6eec3f10af\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4afb199f8169321ff870be33d1a94c7d\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4afb199f8169321ff870be33d1a94c7d\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\72b4e606400e8fbe6689466b853a46c7\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\72b4e606400e8fbe6689466b853a46c7\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0d7db3122fa77f45ef09003065bf7e8\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0d7db3122fa77f45ef09003065bf7e8\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c8de9522c5601c5c4191942ee422185\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c8de9522c5601c5c4191942ee422185\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\acbdce7833fa3726cbbd9257a9ca9cfd\transformed\navigation-compose-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\acbdce7833fa3726cbbd9257a9ca9cfd\transformed\navigation-compose-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\dad28f5405e8ecd83a477663f8c93b32\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\dad28f5405e8ecd83a477663f8c93b32\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43d3c5043fe024bcdb18099973601d18\transformed\material-ripple-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43d3c5043fe024bcdb18099973601d18\transformed\material-ripple-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8a76693bcf85482c2907b9bb520aee8a\transformed\material-icons-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8a76693bcf85482c2907b9bb520aee8a\transformed\material-icons-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\783b1771d7d4fa7637c00d77f327f6f7\transformed\material-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\783b1771d7d4fa7637c00d77f327f6f7\transformed\material-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\247b15fb3656b83390c338c9acf24a5d\transformed\animation-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\247b15fb3656b83390c338c9acf24a5d\transformed\animation-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\990b427b3b7c13ccce1212e67eb42f60\transformed\animation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\990b427b3b7c13ccce1212e67eb42f60\transformed\animation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2e2013cd2a1ebee7c237b884a99e710e\transformed\foundation-layout-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2e2013cd2a1ebee7c237b884a99e710e\transformed\foundation-layout-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\080af24b86e7a736fb1d2c984e82b7d9\transformed\foundation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\080af24b86e7a736fb1d2c984e82b7d9\transformed\foundation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\e227eadf7be79548f6e8e78f88680e43\transformed\ui-tooling-data-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\e227eadf7be79548f6e8e78f88680e43\transformed\ui-tooling-data-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\d909281ac61eae8c7e7efb504f9cf765\transformed\ui-unit-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\d909281ac61eae8c7e7efb504f9cf765\transformed\ui-unit-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\5f837dc2e580a9121c71e88ce1bd7515\transformed\ui-geometry-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\5f837dc2e580a9121c71e88ce1bd7515\transformed\ui-geometry-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\9cf4d73608557c13a2334b7b5e07448a\transformed\ui-util-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\9cf4d73608557c13a2334b7b5e07448a\transformed\ui-util-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\3b7fbc2d3b16ce5f21357a1f8a807645\transformed\ui-text-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\3b7fbc2d3b16ce5f21357a1f8a807645\transformed\ui-text-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\090eebd3d32e73be9b29a435a3806f19\transformed\ui-tooling-preview-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\090eebd3d32e73be9b29a435a3806f19\transformed\ui-tooling-preview-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\fa313ba462e989ede190af542803e7b9\transformed\ui-graphics-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\fa313ba462e989ede190af542803e7b9\transformed\ui-graphics-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\0680b79097ef8fc5aeaed9adf5e353a2\transformed\camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\0680b79097ef8fc5aeaed9adf5e353a2\transformed\camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dd1b3a9f23fc52e969f73e7935c2a62\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dd1b3a9f23fc52e969f73e7935c2a62\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\63bd5e984072d7829c453ef1d8a9d80b\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\63bd5e984072d7829c453ef1d8a9d80b\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\6657ccc8b5733031a445c86dd277f1e3\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\6657ccc8b5733031a445c86dd277f1e3\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\3bfbc17fc8a77550b40416dafcf83e54\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\3bfbc17fc8a77550b40416dafcf83e54\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9634d3098cf4280fd9535641350d796\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9634d3098cf4280fd9535641350d796\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2f933205bace544606e07716f0ee6247\transformed\ui-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2f933205bace544606e07716f0ee6247\transformed\ui-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d625ef4a8e1c099c507118cf27c90eab\transformed\camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d625ef4a8e1c099c507118cf27c90eab\transformed\camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e2d12991baf79c8e1f44a4805ba7ca42\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e2d12991baf79c8e1f44a4805ba7ca42\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\389b0422173fdf5426d5b04ab3b53b5a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\389b0422173fdf5426d5b04ab3b53b5a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f438c4d62abd4ef0e7fa7a9171f3a99\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f438c4d62abd4ef0e7fa7a9171f3a99\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\49a20fe668bf08b4f0874cc61af9ae3c\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\49a20fe668bf08b4f0874cc61af9ae3c\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\267a358f6732eaa66a728f3aae7ef582\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\267a358f6732eaa66a728f3aae7ef582\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\480c28e510a3eb34ebe93f33b9eb1b6a\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\480c28e510a3eb34ebe93f33b9eb1b6a\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6004c5fc0ef70de5dbc64d4996f27a7b\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6004c5fc0ef70de5dbc64d4996f27a7b\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8cac778c5b5014b48464f1fb9ec0e5f\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8cac778c5b5014b48464f1fb9ec0e5f\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\89826be8eb5db12a93d8900fb913d71c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\89826be8eb5db12a93d8900fb913d71c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\76536efdb59467b2f42d891a4d02cccf\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\76536efdb59467b2f42d891a4d02cccf\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1581537c4bd667c24120fae23f8ecf3f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1581537c4bd667c24120fae23f8ecf3f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b016eae976ad658305d804badf89321b\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b016eae976ad658305d804badf89321b\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6b206a5be7699569a0f8417f3efeaf2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6b206a5be7699569a0f8417f3efeaf2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\057092178933cdb1b322af828244aca3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\057092178933cdb1b322af828244aca3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88400a7129850269a5e50ed9dbb8d5e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c88400a7129850269a5e50ed9dbb8d5e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\93bdf84ab7205201c0b1c9343d2dd603\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\93bdf84ab7205201c0b1c9343d2dd603\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\081b130133ac2b62a3c0e4dea88a268d\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\081b130133ac2b62a3c0e4dea88a268d\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\19e4030d9138be6659621bb8f10fc4d8\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\19e4030d9138be6659621bb8f10fc4d8\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71dec1ee4cd0b03b81f745c0cb6bda4e\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71dec1ee4cd0b03b81f745c0cb6bda4e\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4eaaabcb6df0d2483ee61c2c7a7d10e6\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4eaaabcb6df0d2483ee61c2c7a7d10e6\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\5adb8eea175570bf13de2e33f0d35e70\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\5adb8eea175570bf13de2e33f0d35e70\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\af82cc9f5e3603381a170f2db6b05b1c\transformed\runtime-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\af82cc9f5e3603381a170f2db6b05b1c\transformed\runtime-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:16:5-18:41
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a044d2b3f6638ed43054eec441fbceb5\transformed\tensorflow-lite-support-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a044d2b3f6638ed43054eec441fbceb5\transformed\tensorflow-lite-support-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b463632e2d8961bbfc544acd065497f\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b463632e2d8961bbfc544acd065497f\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\9100ae86a1381429f4bf31308028bf90\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\9100ae86a1381429f4bf31308028bf90\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0fafc20f315e4de42cf14ab8ac94f56\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0fafc20f315e4de42cf14ab8ac94f56\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cab3b499e334996126f4f47c78f8f4f5\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cab3b499e334996126f4f47c78f8f4f5\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\959f993d5ff15c8535eb5290d4983785\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\959f993d5ff15c8535eb5290d4983785\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54e19ededdf485f17df3fdff51655f8f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54e19ededdf485f17df3fdff51655f8f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2001b411a6caddb6ab9573e39eb00977\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2001b411a6caddb6ab9573e39eb00977\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7107afd79c504ad9b646039c49df771a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7107afd79c504ad9b646039c49df771a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\949176be70d371c5bc7d1a8796ab5a91\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\949176be70d371c5bc7d1a8796ab5a91\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce6ec2b410ccee7ec8662ccb55001e36\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce6ec2b410ccee7ec8662ccb55001e36\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1c3c22c5dd481e17badb3db1c2fce852\transformed\tensorflow-lite-support-api-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1c3c22c5dd481e17badb3db1c2fce852\transformed\tensorflow-lite-support-api-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\2939904060c386b8b41d61590198f493\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\2939904060c386b8b41d61590198f493\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\9ef5760ac40652b661f92ec1d777ca5a\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\9ef5760ac40652b661f92ec1d777ca5a\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\43e6569ac27e856965cfce6eec3f10af\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\1fd7e66ea1b766673294b78a6c24c538\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\99cfd0eea17aa0c18b5413459a6e4138\transformed\ui-tooling-1.4.3\AndroidManifest.xml:24:13-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\d9c19127be19e49a0fd2dac504153525\transformed\camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\80cbba5af0afe8c63388af4730920828\transformed\camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e5033ae2695800e5aaf79453d4afc64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1d6e2e1ef906b36aeacea897da88537\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\562d44b5b71d88e1ca2bcbeaaa31ed66\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.measure.ar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.measure.ar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\00ff855e8f1c322d6d01ddd31185d1a4\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
queries
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:20:5-22:15
package#com.google.ar.core
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:21:9-54
	android:name
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:21:18-51
meta-data#com.google.ar.core.min_apk_version
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:27:9-29:41
	android:value
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:29:13-38
	android:name
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:28:13-62
activity#com.google.ar.core.InstallActivity
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:31:9-37:80
	android:excludeFromRecents
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:34:13-46
	android:launchMode
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:36:13-43
	android:exported
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:35:13-37
	android:configChanges
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:33:13-74
	android:theme
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:37:13-77
	android:name
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\transforms-3\08bdf27b58921855d050243aa9159418\transformed\core-1.39.0\AndroidManifest.xml:32:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3fb16dc11420a4c79467d8ca95611a6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
