-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\418ed594d2f982d9db35e8115fae3bcb\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96061721dd91bbc40fa168f325a9a6cb\transformed\navigation-common-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fa0d5b0caebc45faed682b564b68666\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70043bb3529adc789b5e396823caa1a1\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afac09b7e59b6b75ab09b5fe4edd0220\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7619870e7ddd1a0fe16c2de943b61ed6\transformed\navigation-compose-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd65c2e74f7987a8d8deabfe5646d3df\transformed\camera-view-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dad0f1c303d9c03d4f8d987e69814464\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\144de6ed54e2a9f8bb523745bed0669e\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bed37921ac964737d07794cf4c9a434\transformed\material3-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41d255c69c5c8b1da193ad3a7aec07f4\transformed\material-ripple-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78d38809f814d404088fd0f074d6c9a\transformed\material-icons-core-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc21702fb70a57665960b23d65edb345\transformed\material-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54662bd82f3abf91f8862651c4362076\transformed\animation-core-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e27fd72ce5165c06b06681e66694b86\transformed\animation-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70c86a0d399e32a338a1ead3737d24c\transformed\foundation-layout-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a515d1c27c581bd304e063ad70259e\transformed\foundation-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eebf3fbee21a2854e1901df8fc0d78be\transformed\ui-tooling-data-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975df338f2d4aff0c1cba9f03f3313fa\transformed\ui-unit-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc85d9e150ec015762edb18358e68f0\transformed\ui-geometry-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c1b4fd990e2aad44ade0a5d6a6d3ffb\transformed\ui-util-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71bea5d8f965abda340353d2fa2e96ce\transformed\ui-text-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be5a92550f6970a154ae999fc1ea7bbe\transformed\ui-tooling-preview-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc90cfb5f97758342588205dfaa2360\transformed\ui-graphics-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab61ae0b93a52bdb5e7865b9dbce3f5\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73a1eb11ff81e3fe66912f8f862a3b28\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af74c47ecc858079afe440f016862f71\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3351e0537f10a141173bea58e5572472\transformed\activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d3a7436c8f640fe5c04b2f0f4278ea\transformed\ui-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e24fad2ee431f4ebec1e252b960dd35\transformed\camera-lifecycle-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce8c610aee55743930f87533aae4afd\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f353d4dbc566a71ace7c22ec9c4c5b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907e6700997bc198ee02291c5fa1588\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47195dcd5a33cb3bd8c34a5700cbc0f6\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a93b0b12eff5be1122a700ee868521b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f94a8b41a05f660d3dbc59415007f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da8cc8f376bd7eee5bcddaa07b57eadf\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a77b49f1e2940d7340eb2e5657cd6006\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad41c2b14f81329dce4f266cd370fb17\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0266cc2f32cf5f0428cb7df0800c92c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00bfe0ed96a86e7ad0cf652a532b570c\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73c25851f83dc9861bf1c4c39425093d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9a616172cc0bba8f024981c213065c9\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a11329b4a61a6c38cf6b1a505c534d4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48b4082373ded41f99f9c9cd6dde96e8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\403309af1bcb5a4f502f96253f72907d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5537fc541208aff5cb881bddcb72062e\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2dc52b9c57562e70dbcd42195c1b7a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efcadde9a3cdc8bf1dc260c71efebc4d\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a245bf1421adb18259795386eb41802\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb6dd19e12fa4ca2d737959d45c5eefc\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5080abf429dc1109e8967a0470af1d\transformed\runtime-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:8:1-40:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be34a8ddcc6065c01913d72bdd56003\transformed\tensorflow-lite-support-0.4.3\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e27b64e93ff37853841fe0efff1b70\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7a915af3f95fd65e24030c1453f957b\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179a0d59e3650b5764d9f1fd74016dba\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8979b68747b3315f6430d2402c922b9\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ae44108d396f9ea3817ac6a5979d58a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc7d8785ccc387443801bf91ccde9634\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49eeffc43b72179f8ad5e473bde4525b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860c243c5728aef82d835122ed3c9c0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e47ec7508ad484d5e5a7de908bd46fa\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\688fb4d7bd8cec2136c5fe9e07eee653\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d44e14872cbddf4e258b76debf17117f\transformed\tensorflow-lite-support-api-0.4.3\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54db7d98727496e68a7a1ddfa2d73f3a\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d6bbc62aafaa72250779831beb8f9ab\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:7:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:11:5-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:11:22-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:15:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:16:22-77
uses-feature#android.hardware.camera.ar
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:5-87
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:61-84
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:19-60
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:5-98
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:72-95
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:19-71
uses-feature#android.hardware.sensor.gyroscope
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:5-94
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:68-91
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:19-67
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:24:5-50:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:24:5-50:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:24:5-38:19
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:24:5-38:19
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e27b64e93ff37853841fe0efff1b70\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e27b64e93ff37853841fe0efff1b70\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7a915af3f95fd65e24030c1453f957b\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7a915af3f95fd65e24030c1453f957b\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860c243c5728aef82d835122ed3c9c0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860c243c5728aef82d835122ed3c9c0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54db7d98727496e68a7a1ddfa2d73f3a\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54db7d98727496e68a7a1ddfa2d73f3a\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d6bbc62aafaa72250779831beb8f9ab\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d6bbc62aafaa72250779831beb8f9ab\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:31:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:29:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:27:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:30:9-62
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:33:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:28:9-57
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:25:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:32:9-45
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:26:9-65
meta-data#com.google.ar.core
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:9-81
	android:value
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:54-78
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:20-53
activity#com.measure.ar.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:38:9-49:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:43:13-49
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:41:13-45
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:40:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:44:13-74
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:42:13-49
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:39:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:45:13-48:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:46:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:46:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:47:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:47:27-74
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\418ed594d2f982d9db35e8115fae3bcb\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\418ed594d2f982d9db35e8115fae3bcb\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96061721dd91bbc40fa168f325a9a6cb\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96061721dd91bbc40fa168f325a9a6cb\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fa0d5b0caebc45faed682b564b68666\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fa0d5b0caebc45faed682b564b68666\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70043bb3529adc789b5e396823caa1a1\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70043bb3529adc789b5e396823caa1a1\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afac09b7e59b6b75ab09b5fe4edd0220\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afac09b7e59b6b75ab09b5fe4edd0220\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7619870e7ddd1a0fe16c2de943b61ed6\transformed\navigation-compose-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7619870e7ddd1a0fe16c2de943b61ed6\transformed\navigation-compose-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd65c2e74f7987a8d8deabfe5646d3df\transformed\camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd65c2e74f7987a8d8deabfe5646d3df\transformed\camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dad0f1c303d9c03d4f8d987e69814464\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dad0f1c303d9c03d4f8d987e69814464\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\144de6ed54e2a9f8bb523745bed0669e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\144de6ed54e2a9f8bb523745bed0669e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bed37921ac964737d07794cf4c9a434\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bed37921ac964737d07794cf4c9a434\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41d255c69c5c8b1da193ad3a7aec07f4\transformed\material-ripple-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41d255c69c5c8b1da193ad3a7aec07f4\transformed\material-ripple-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78d38809f814d404088fd0f074d6c9a\transformed\material-icons-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78d38809f814d404088fd0f074d6c9a\transformed\material-icons-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc21702fb70a57665960b23d65edb345\transformed\material-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc21702fb70a57665960b23d65edb345\transformed\material-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54662bd82f3abf91f8862651c4362076\transformed\animation-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54662bd82f3abf91f8862651c4362076\transformed\animation-core-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e27fd72ce5165c06b06681e66694b86\transformed\animation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e27fd72ce5165c06b06681e66694b86\transformed\animation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70c86a0d399e32a338a1ead3737d24c\transformed\foundation-layout-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70c86a0d399e32a338a1ead3737d24c\transformed\foundation-layout-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a515d1c27c581bd304e063ad70259e\transformed\foundation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a515d1c27c581bd304e063ad70259e\transformed\foundation-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eebf3fbee21a2854e1901df8fc0d78be\transformed\ui-tooling-data-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eebf3fbee21a2854e1901df8fc0d78be\transformed\ui-tooling-data-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975df338f2d4aff0c1cba9f03f3313fa\transformed\ui-unit-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975df338f2d4aff0c1cba9f03f3313fa\transformed\ui-unit-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc85d9e150ec015762edb18358e68f0\transformed\ui-geometry-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc85d9e150ec015762edb18358e68f0\transformed\ui-geometry-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c1b4fd990e2aad44ade0a5d6a6d3ffb\transformed\ui-util-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c1b4fd990e2aad44ade0a5d6a6d3ffb\transformed\ui-util-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71bea5d8f965abda340353d2fa2e96ce\transformed\ui-text-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71bea5d8f965abda340353d2fa2e96ce\transformed\ui-text-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be5a92550f6970a154ae999fc1ea7bbe\transformed\ui-tooling-preview-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be5a92550f6970a154ae999fc1ea7bbe\transformed\ui-tooling-preview-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc90cfb5f97758342588205dfaa2360\transformed\ui-graphics-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc90cfb5f97758342588205dfaa2360\transformed\ui-graphics-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab61ae0b93a52bdb5e7865b9dbce3f5\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab61ae0b93a52bdb5e7865b9dbce3f5\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73a1eb11ff81e3fe66912f8f862a3b28\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73a1eb11ff81e3fe66912f8f862a3b28\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af74c47ecc858079afe440f016862f71\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af74c47ecc858079afe440f016862f71\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3351e0537f10a141173bea58e5572472\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3351e0537f10a141173bea58e5572472\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d3a7436c8f640fe5c04b2f0f4278ea\transformed\ui-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d3a7436c8f640fe5c04b2f0f4278ea\transformed\ui-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e24fad2ee431f4ebec1e252b960dd35\transformed\camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e24fad2ee431f4ebec1e252b960dd35\transformed\camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce8c610aee55743930f87533aae4afd\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce8c610aee55743930f87533aae4afd\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f353d4dbc566a71ace7c22ec9c4c5b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f353d4dbc566a71ace7c22ec9c4c5b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907e6700997bc198ee02291c5fa1588\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907e6700997bc198ee02291c5fa1588\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47195dcd5a33cb3bd8c34a5700cbc0f6\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47195dcd5a33cb3bd8c34a5700cbc0f6\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a93b0b12eff5be1122a700ee868521b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a93b0b12eff5be1122a700ee868521b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f94a8b41a05f660d3dbc59415007f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f94a8b41a05f660d3dbc59415007f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da8cc8f376bd7eee5bcddaa07b57eadf\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da8cc8f376bd7eee5bcddaa07b57eadf\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a77b49f1e2940d7340eb2e5657cd6006\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a77b49f1e2940d7340eb2e5657cd6006\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad41c2b14f81329dce4f266cd370fb17\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad41c2b14f81329dce4f266cd370fb17\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0266cc2f32cf5f0428cb7df0800c92c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0266cc2f32cf5f0428cb7df0800c92c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00bfe0ed96a86e7ad0cf652a532b570c\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00bfe0ed96a86e7ad0cf652a532b570c\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73c25851f83dc9861bf1c4c39425093d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73c25851f83dc9861bf1c4c39425093d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9a616172cc0bba8f024981c213065c9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9a616172cc0bba8f024981c213065c9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a11329b4a61a6c38cf6b1a505c534d4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a11329b4a61a6c38cf6b1a505c534d4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48b4082373ded41f99f9c9cd6dde96e8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48b4082373ded41f99f9c9cd6dde96e8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\403309af1bcb5a4f502f96253f72907d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\403309af1bcb5a4f502f96253f72907d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5537fc541208aff5cb881bddcb72062e\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5537fc541208aff5cb881bddcb72062e\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2dc52b9c57562e70dbcd42195c1b7a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2dc52b9c57562e70dbcd42195c1b7a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efcadde9a3cdc8bf1dc260c71efebc4d\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efcadde9a3cdc8bf1dc260c71efebc4d\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a245bf1421adb18259795386eb41802\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a245bf1421adb18259795386eb41802\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb6dd19e12fa4ca2d737959d45c5eefc\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb6dd19e12fa4ca2d737959d45c5eefc\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5080abf429dc1109e8967a0470af1d\transformed\runtime-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5080abf429dc1109e8967a0470af1d\transformed\runtime-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:16:5-18:41
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be34a8ddcc6065c01913d72bdd56003\transformed\tensorflow-lite-support-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be34a8ddcc6065c01913d72bdd56003\transformed\tensorflow-lite-support-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e27b64e93ff37853841fe0efff1b70\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e27b64e93ff37853841fe0efff1b70\transformed\tensorflow-lite-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7a915af3f95fd65e24030c1453f957b\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-gpu:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7a915af3f95fd65e24030c1453f957b\transformed\tensorflow-lite-gpu-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179a0d59e3650b5764d9f1fd74016dba\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179a0d59e3650b5764d9f1fd74016dba\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8979b68747b3315f6430d2402c922b9\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8979b68747b3315f6430d2402c922b9\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ae44108d396f9ea3817ac6a5979d58a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ae44108d396f9ea3817ac6a5979d58a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc7d8785ccc387443801bf91ccde9634\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc7d8785ccc387443801bf91ccde9634\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49eeffc43b72179f8ad5e473bde4525b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49eeffc43b72179f8ad5e473bde4525b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860c243c5728aef82d835122ed3c9c0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860c243c5728aef82d835122ed3c9c0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e47ec7508ad484d5e5a7de908bd46fa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e47ec7508ad484d5e5a7de908bd46fa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\688fb4d7bd8cec2136c5fe9e07eee653\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\688fb4d7bd8cec2136c5fe9e07eee653\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d44e14872cbddf4e258b76debf17117f\transformed\tensorflow-lite-support-api-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d44e14872cbddf4e258b76debf17117f\transformed\tensorflow-lite-support-api-0.4.3\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54db7d98727496e68a7a1ddfa2d73f3a\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54db7d98727496e68a7a1ddfa2d73f3a\transformed\tensorflow-lite-api-2.13.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d6bbc62aafaa72250779831beb8f9ab\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d6bbc62aafaa72250779831beb8f9ab\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\418ed594d2f982d9db35e8115fae3bcb\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:24:13-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.measure.ar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.measure.ar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
queries
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:20:5-22:15
package#com.google.ar.core
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:21:9-54
	android:name
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:21:18-51
meta-data#com.google.ar.core.min_apk_version
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:27:9-29:41
	android:value
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:29:13-38
	android:name
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:28:13-62
activity#com.google.ar.core.InstallActivity
ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:31:9-37:80
	android:excludeFromRecents
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:34:13-46
	android:launchMode
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:36:13-43
	android:exported
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:35:13-37
	android:configChanges
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:33:13-74
	android:theme
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:37:13-77
	android:name
		ADDED from [com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:32:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
