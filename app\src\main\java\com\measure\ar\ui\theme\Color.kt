package com.measure.ar.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Measurement specific colors
val MeasureBlue = Color(0xFF2196F3)
val MeasureRed = Color(0xFFF44336)
val MeasureGreen = Color(0xFF4CAF50)
val MeasureOrange = Color(0xFFFF9800)

// AR UI colors
val AROverlay = Color(0x80000000)
val ARReticle = Color(0xFFFFFFFF)
val ARMeasureLine = Color(0xFF00BCD4)
val ARSnapIndicator = Color(0xFF8BC34A)
