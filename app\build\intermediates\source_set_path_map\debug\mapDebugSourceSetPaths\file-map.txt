com.measure.ar.app-core-ktx-1.10.1-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00bfe0ed96a86e7ad0cf652a532b570c\transformed\core-ktx-1.10.1\res
com.measure.ar.app-foundation-1.4.3-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a515d1c27c581bd304e063ad70259e\transformed\foundation-1.4.3\res
com.measure.ar.app-runtime-1.4.3-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5080abf429dc1109e8967a0470af1d\transformed\runtime-1.4.3\res
com.measure.ar.app-camera-lifecycle-1.2.3-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e24fad2ee431f4ebec1e252b960dd35\transformed\camera-lifecycle-1.2.3\res
com.measure.ar.app-appcompat-1.6.1-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\144de6ed54e2a9f8bb523745bed0669e\transformed\appcompat-1.6.1\res
com.measure.ar.app-annotation-experimental-1.3.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179a0d59e3650b5764d9f1fd74016dba\transformed\annotation-experimental-1.3.0\res
com.measure.ar.app-lifecycle-runtime-ktx-2.6.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a245bf1421adb18259795386eb41802\transformed\lifecycle-runtime-ktx-2.6.1\res
com.measure.ar.app-ui-graphics-1.4.3-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc90cfb5f97758342588205dfaa2360\transformed\ui-graphics-1.4.3\res
com.measure.ar.app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2511eb3e4821c130b27eb525c867366e\transformed\startup-runtime-1.1.1\res
com.measure.ar.app-ui-util-1.4.3-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c1b4fd990e2aad44ade0a5d6a6d3ffb\transformed\ui-util-1.4.3\res
com.measure.ar.app-animation-1.4.3-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e27fd72ce5165c06b06681e66694b86\transformed\animation-1.4.3\res
com.measure.ar.app-activity-compose-1.7.2-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3351e0537f10a141173bea58e5572472\transformed\activity-compose-1.7.2\res
com.measure.ar.app-core-1.39.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\res
com.measure.ar.app-material-ripple-1.4.3-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41d255c69c5c8b1da193ad3a7aec07f4\transformed\material-ripple-1.4.3\res
com.measure.ar.app-lifecycle-livedata-2.6.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47195dcd5a33cb3bd8c34a5700cbc0f6\transformed\lifecycle-livedata-2.6.1\res
com.measure.ar.app-camera-camera2-1.2.3-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\res
com.measure.ar.app-lifecycle-process-2.6.1-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\res
com.measure.ar.app-animation-core-1.4.3-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54662bd82f3abf91f8862651c4362076\transformed\animation-core-1.4.3\res
com.measure.ar.app-ui-tooling-1.4.3-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\res
com.measure.ar.app-ui-geometry-1.4.3-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc85d9e150ec015762edb18358e68f0\transformed\ui-geometry-1.4.3\res
com.measure.ar.app-emoji2-1.3.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\res
com.measure.ar.app-core-1.10.1-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\res
com.measure.ar.app-lifecycle-livedata-core-2.6.1-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6907e6700997bc198ee02291c5fa1588\transformed\lifecycle-livedata-core-2.6.1\res
com.measure.ar.app-lifecycle-runtime-2.6.1-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e2dc52b9c57562e70dbcd42195c1b7a\transformed\lifecycle-runtime-2.6.1\res
com.measure.ar.app-navigation-runtime-2.6.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fa0d5b0caebc45faed682b564b68666\transformed\navigation-runtime-2.6.0\res
com.measure.ar.app-navigation-common-ktx-2.6.0-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70043bb3529adc789b5e396823caa1a1\transformed\navigation-common-ktx-2.6.0\res
com.measure.ar.app-camera-core-1.2.3-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713add4ed7d392bd6dad3eb47312bf92\transformed\camera-core-1.2.3\res
com.measure.ar.app-ui-text-1.4.3-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71bea5d8f965abda340353d2fa2e96ce\transformed\ui-text-1.4.3\res
com.measure.ar.app-activity-ktx-1.7.2-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73a1eb11ff81e3fe66912f8f862a3b28\transformed\activity-ktx-1.7.2\res
com.measure.ar.app-navigation-compose-2.6.0-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7619870e7ddd1a0fe16c2de943b61ed6\transformed\navigation-compose-2.6.0\res
com.measure.ar.app-core-runtime-2.2.0-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e47ec7508ad484d5e5a7de908bd46fa\transformed\core-runtime-2.2.0\res
com.measure.ar.app-material3-1.1.1-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bed37921ac964737d07794cf4c9a434\transformed\material3-1.1.1\res
com.measure.ar.app-ui-test-manifest-1.4.3-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\res
com.measure.ar.app-profileinstaller-1.3.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\res
com.measure.ar.app-navigation-common-2.6.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96061721dd91bbc40fa168f325a9a6cb\transformed\navigation-common-2.6.0\res
com.measure.ar.app-ui-unit-1.4.3-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975df338f2d4aff0c1cba9f03f3313fa\transformed\ui-unit-1.4.3\res
com.measure.ar.app-savedstate-ktx-1.2.1-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a93b0b12eff5be1122a700ee868521b\transformed\savedstate-ktx-1.2.1\res
com.measure.ar.app-customview-poolingcontainer-1.0.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0266cc2f32cf5f0428cb7df0800c92c\transformed\customview-poolingcontainer-1.0.0\res
com.measure.ar.app-lifecycle-viewmodel-2.6.1-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a77b49f1e2940d7340eb2e5657cd6006\transformed\lifecycle-viewmodel-2.6.1\res
com.measure.ar.app-ui-1.4.3-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d3a7436c8f640fe5c04b2f0f4278ea\transformed\ui-1.4.3\res
com.measure.ar.app-lifecycle-viewmodel-savedstate-2.6.1-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad41c2b14f81329dce4f266cd370fb17\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.measure.ar.app-activity-1.7.2-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af74c47ecc858079afe440f016862f71\transformed\activity-1.7.2\res
com.measure.ar.app-navigation-runtime-ktx-2.6.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afac09b7e59b6b75ab09b5fe4edd0220\transformed\navigation-runtime-ktx-2.6.0\res
com.measure.ar.app-foundation-layout-1.4.3-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70c86a0d399e32a338a1ead3737d24c\transformed\foundation-layout-1.4.3\res
com.measure.ar.app-ui-tooling-preview-1.4.3-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be5a92550f6970a154ae999fc1ea7bbe\transformed\ui-tooling-preview-1.4.3\res
com.measure.ar.app-material-icons-core-1.4.3-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78d38809f814d404088fd0f074d6c9a\transformed\material-icons-core-1.4.3\res
com.measure.ar.app-material-1.4.3-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc21702fb70a57665960b23d65edb345\transformed\material-1.4.3\res
com.measure.ar.app-lifecycle-viewmodel-ktx-2.6.1-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da8cc8f376bd7eee5bcddaa07b57eadf\transformed\lifecycle-viewmodel-ktx-2.6.1\res
com.measure.ar.app-appcompat-resources-1.6.1-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dad0f1c303d9c03d4f8d987e69814464\transformed\appcompat-resources-1.6.1\res
com.measure.ar.app-runtime-saveable-1.4.3-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb6dd19e12fa4ca2d737959d45c5eefc\transformed\runtime-saveable-1.4.3\res
com.measure.ar.app-ui-tooling-data-1.4.3-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eebf3fbee21a2854e1901df8fc0d78be\transformed\ui-tooling-data-1.4.3\res
com.measure.ar.app-lifecycle-viewmodel-compose-2.6.1-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efcadde9a3cdc8bf1dc260c71efebc4d\transformed\lifecycle-viewmodel-compose-2.6.1\res
com.measure.ar.app-emoji2-views-helper-1.3.0-52 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f353d4dbc566a71ace7c22ec9c4c5b\transformed\emoji2-views-helper-1.3.0\res
com.measure.ar.app-savedstate-1.2.1-53 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f94a8b41a05f660d3dbc59415007f\transformed\savedstate-1.2.1\res
com.measure.ar.app-fragment-1.3.6-54 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab61ae0b93a52bdb5e7865b9dbce3f5\transformed\fragment-1.3.6\res
com.measure.ar.app-camera-view-1.2.3-55 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd65c2e74f7987a8d8deabfe5646d3df\transformed\camera-view-1.2.3\res
com.measure.ar.app-resValues-56 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\generated\res\resValues\debug
com.measure.ar.app-packageDebugResources-57 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.measure.ar.app-packageDebugResources-58 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.measure.ar.app-debug-59 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\build\intermediates\merged_res\debug\mergeDebugResources
com.measure.ar.app-debug-60 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\debug\res
com.measure.ar.app-main-61 C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\res
