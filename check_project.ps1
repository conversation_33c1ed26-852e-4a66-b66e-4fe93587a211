Write-Host "=== Measure AR App - Project Check ===" -ForegroundColor Cyan
Write-Host ""

# Check key files
$files = @(
    "build.gradle",
    "app\build.gradle", 
    "app\src\main\AndroidManifest.xml",
    "README.md",
    "SETUP_GUIDE.md"
)

Write-Host "Project Files:" -ForegroundColor Yellow
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $file" -ForegroundColor Red
    }
}

Write-Host ""

# Check Java
Write-Host "Java Check:" -ForegroundColor Yellow
try {
    $null = Get-Command java -ErrorAction Stop
    Write-Host "  ✓ Java is available" -ForegroundColor Green
} catch {
    Write-Host "  ✗ Java not found - Install JDK 11+" -ForegroundColor Red
}

Write-Host ""

# Count source files
$kotlinFiles = Get-ChildItem -Path "app\src" -Filter "*.kt" -Recurse -ErrorAction SilentlyContinue
if ($kotlinFiles) {
    Write-Host "Source Files: $($kotlinFiles.Count) Kotlin files" -ForegroundColor Green
} else {
    Write-Host "Source Files: No Kotlin files found" -ForegroundColor Red
}

Write-Host ""
Write-Host "Next: Follow SETUP_GUIDE.md for complete setup instructions" -ForegroundColor Cyan
