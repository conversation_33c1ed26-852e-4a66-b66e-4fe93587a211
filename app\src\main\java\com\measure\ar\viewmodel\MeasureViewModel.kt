package com.measure.ar.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.measure.ar.ar.AREngine
import com.measure.ar.data.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * Main ViewModel for the Measure app
 */
class MeasureViewModel(application: Application) : AndroidViewModel(application) {
    
    private val arEngine = AREngine(application)
    
    // UI State
    private val _uiState = MutableStateFlow(MeasureUiState())
    val uiState: StateFlow<MeasureUiState> = _uiState.asStateFlow()
    
    // Current measurement session
    private val _currentSession = MutableStateFlow(MeasurementSession(id = generateSessionId()))
    val currentSession: StateFlow<MeasurementSession> = _currentSession.asStateFlow()
    
    // AR session state
    val arSessionState = arEngine.sessionState
    val trackingQuality = arEngine.trackingQuality
    
    // Current measurement in progress
    private val _currentMeasurement = MutableStateFlow<Measurement?>(null)
    val currentMeasurement: StateFlow<Measurement?> = _currentMeasurement.asStateFlow()
    
    init {
        initializeAR()
    }
    
    private fun initializeAR() {
        viewModelScope.launch {
            val success = arEngine.initialize()
            _uiState.value = _uiState.value.copy(
                isARInitialized = success,
                isLoading = false
            )
        }
    }
    
    /**
     * Start a new measurement
     */
    fun startMeasurement(type: MeasurementType) {
        _uiState.value = _uiState.value.copy(
            currentMeasurementType = type,
            isMeasuring = true,
            measurementPoints = emptyList()
        )
    }
    
    /**
     * Add a measurement point
     */
    fun addMeasurementPoint(x: Float, y: Float) {
        val hitResults = arEngine.hitTest(x, y)
        if (hitResults.isNotEmpty()) {
            val hitResult = hitResults.first()
            val pose = hitResult.hitPose
            val point = Point3D(pose.tx(), pose.ty(), pose.tz())
            
            val currentPoints = _uiState.value.measurementPoints
            val newPoints = currentPoints + point
            
            _uiState.value = _uiState.value.copy(
                measurementPoints = newPoints
            )
            
            // If we have enough points for the current measurement type, complete it
            when (_uiState.value.currentMeasurementType) {
                MeasurementType.SINGLE_LINE -> {
                    if (newPoints.size == 2) {
                        completeMeasurement()
                    }
                }
                MeasurementType.MULTI_SEGMENT -> {
                    // Continue adding points until user explicitly finishes
                }
                MeasurementType.AREA -> {
                    if (newPoints.size >= 3) {
                        // Can complete area measurement
                    }
                }
                else -> {}
            }
        }
    }
    
    /**
     * Complete the current measurement
     */
    fun completeMeasurement() {
        val points = _uiState.value.measurementPoints
        if (points.size >= 2) {
            val measurement = when (_uiState.value.currentMeasurementType) {
                MeasurementType.SINGLE_LINE -> {
                    createLineMeasurement(points[0], points[1])
                }
                MeasurementType.MULTI_SEGMENT -> {
                    createMultiSegmentMeasurement(points)
                }
                MeasurementType.AREA -> {
                    createAreaMeasurement(points)
                }
                else -> null
            }
            
            measurement?.let { addMeasurementToSession(it) }
        }
        
        // Reset measurement state
        _uiState.value = _uiState.value.copy(
            isMeasuring = false,
            measurementPoints = emptyList(),
            currentMeasurementType = null
        )
    }
    
    private fun createLineMeasurement(point1: Point3D, point2: Point3D): Measurement {
        val distance = point1.distanceTo(point2)
        return Measurement(
            id = generateMeasurementId(),
            type = MeasurementType.SINGLE_LINE,
            points = listOf(point1, point2),
            value = distance,
            quality = trackingQuality.value,
            confidence = calculateConfidence(trackingQuality.value)
        )
    }
    
    private fun createMultiSegmentMeasurement(points: List<Point3D>): Measurement {
        var totalDistance = 0f
        for (i in 0 until points.size - 1) {
            totalDistance += points[i].distanceTo(points[i + 1])
        }
        
        return Measurement(
            id = generateMeasurementId(),
            type = MeasurementType.MULTI_SEGMENT,
            points = points,
            value = totalDistance,
            quality = trackingQuality.value,
            confidence = calculateConfidence(trackingQuality.value)
        )
    }
    
    private fun createAreaMeasurement(points: List<Point3D>): Measurement {
        // Simplified area calculation using shoelace formula for 2D projection
        val area = calculatePolygonArea(points)
        
        return Measurement(
            id = generateMeasurementId(),
            type = MeasurementType.AREA,
            points = points,
            value = area,
            unit = "m²",
            quality = trackingQuality.value,
            confidence = calculateConfidence(trackingQuality.value)
        )
    }
    
    private fun calculatePolygonArea(points: List<Point3D>): Float {
        if (points.size < 3) return 0f
        
        var area = 0f
        for (i in points.indices) {
            val j = (i + 1) % points.size
            area += points[i].x * points[j].z - points[j].x * points[i].z
        }
        return kotlin.math.abs(area) / 2f
    }
    
    private fun addMeasurementToSession(measurement: Measurement) {
        val currentMeasurements = _currentSession.value.measurements
        _currentSession.value = _currentSession.value.copy(
            measurements = currentMeasurements + measurement
        )
        _currentMeasurement.value = measurement
    }
    
    /**
     * Start calibration process
     */
    fun startCalibration() {
        arEngine.startCalibration()
        _uiState.value = _uiState.value.copy(isCalibrating = true)
    }
    
    /**
     * Check calibration status
     */
    fun checkCalibration() {
        if (arEngine.isCalibrationComplete()) {
            _uiState.value = _uiState.value.copy(isCalibrating = false)
        }
    }
    
    /**
     * Clear all measurements
     */
    fun clearMeasurements() {
        _currentSession.value = _currentSession.value.copy(measurements = emptyList())
        _currentMeasurement.value = null
    }
    
    /**
     * Update AR session
     */
    fun updateARSession() {
        arEngine.updateSession()
    }
    
    /**
     * Resume AR session
     */
    fun resumeAR() {
        arEngine.resume()
    }
    
    /**
     * Pause AR session
     */
    fun pauseAR() {
        arEngine.pause()
    }
    
    private fun calculateConfidence(quality: MeasurementQuality): Float {
        return when (quality) {
            MeasurementQuality.POOR -> 0.5f
            MeasurementQuality.FAIR -> 0.7f
            MeasurementQuality.GOOD -> 0.9f
            MeasurementQuality.EXCELLENT -> 0.95f
        }
    }
    
    private fun generateSessionId(): String {
        return "session_${System.currentTimeMillis()}"
    }
    
    private fun generateMeasurementId(): String {
        return "measurement_${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
    }
    
    override fun onCleared() {
        super.onCleared()
        arEngine.cleanup()
    }
}

/**
 * UI state for the Measure screen
 */
data class MeasureUiState(
    val isLoading: Boolean = true,
    val isARInitialized: Boolean = false,
    val isMeasuring: Boolean = false,
    val isCalibrating: Boolean = false,
    val currentMeasurementType: MeasurementType? = null,
    val measurementPoints: List<Point3D> = emptyList(),
    val showTutorial: Boolean = false,
    val selectedUnit: String = "cm",
    val error: String? = null
)
