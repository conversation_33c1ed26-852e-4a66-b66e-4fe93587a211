package com.measure.ar.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.measure.ar.data.MeasurementType
import com.measure.ar.ui.theme.AROverlay
import com.measure.ar.ui.theme.MeasureBlue

/**
 * Toolbar with measurement tools and options
 */
@Composable
fun MeasurementToolbar(
    onMeasurementTypeSelected: (MeasurementType) -> Unit,
    onCalibrate: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showToolMenu by remember { mutableStateOf(false) }

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Expandable tool menu
        if (showToolMenu) {
            ToolMenu(
                onMeasurementTypeSelected = { type ->
                    onMeasurementTypeSelected(type)
                    showToolMenu = false
                },
                onCalibrate = onCalibrate,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        // Main action button
        FloatingActionButton(
            onClick = { showToolMenu = !showToolMenu },
            containerColor = MeasureBlue,
            modifier = Modifier.size(64.dp)
        ) {
            Icon(
                imageVector = if (showToolMenu) Icons.Default.Close else Icons.Default.Add,
                contentDescription = if (showToolMenu) "Close tools" else "Open tools",
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

@Composable
private fun ToolMenu(
    onMeasurementTypeSelected: (MeasurementType) -> Unit,
    onCalibrate: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = AROverlay
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Measurement Tools",
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // Measurement type buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                MeasurementTypeButton(
                    icon = Icons.Default.Remove,
                    label = "Line",
                    onClick = { onMeasurementTypeSelected(MeasurementType.SINGLE_LINE) }
                )

                MeasurementTypeButton(
                    icon = Icons.Default.ShowChart,
                    label = "Path",
                    onClick = { onMeasurementTypeSelected(MeasurementType.MULTI_SEGMENT) }
                )

                MeasurementTypeButton(
                    icon = Icons.Default.CropSquare,
                    label = "Area",
                    onClick = { onMeasurementTypeSelected(MeasurementType.AREA) }
                )
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                MeasurementTypeButton(
                    icon = Icons.Default.ViewInAr,
                    label = "Volume",
                    onClick = { onMeasurementTypeSelected(MeasurementType.VOLUME) }
                )

                MeasurementTypeButton(
                    icon = Icons.Default.Build,
                    label = "Level",
                    onClick = { onMeasurementTypeSelected(MeasurementType.LEVEL) }
                )

                MeasurementTypeButton(
                    icon = Icons.Default.Tune,
                    label = "Calibrate",
                    onClick = onCalibrate
                )
            }
        }
    }
}

@Composable
private fun MeasurementTypeButton(
    icon: ImageVector,
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        FloatingActionButton(
            onClick = onClick,
            modifier = Modifier.size(48.dp),
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                modifier = Modifier.size(24.dp),
                tint = MeasureBlue
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = label,
            color = Color.White,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * Quality indicator component
 */
@Composable
fun QualityIndicator(
    quality: com.measure.ar.data.MeasurementQuality,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (quality) {
        com.measure.ar.data.MeasurementQuality.POOR -> Color.Red to "Poor"
        com.measure.ar.data.MeasurementQuality.FAIR -> Color.Yellow to "Fair"
        com.measure.ar.data.MeasurementQuality.GOOD -> Color.Green to "Good"
        com.measure.ar.data.MeasurementQuality.EXCELLENT -> Color.Cyan to "Excellent"
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(CircleShape)
                .background(color)
        )

        Text(
            text = text,
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * Calibration overlay component
 */
@Composable
fun CalibrationOverlay(
    onCalibrationComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    var progress by remember { mutableStateOf(0f) }

    LaunchedEffect(Unit) {
        // Simulate calibration progress
        for (i in 0..100) {
            progress = i / 100f
            kotlinx.coroutines.delay(50)
        }
        onCalibrationComplete()
    }

    Box(
        modifier = modifier.background(AROverlay),
        contentAlignment = Alignment.Center
    ) {
        Card(
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
            )
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Icon(
                    Icons.Default.Tune,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MeasureBlue
                )

                Text(
                    text = "Calibrating Device",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = "Move your device in a figure-8 pattern",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                LinearProgressIndicator(
                    progress = progress,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = MeasureBlue
                )

                Text(
                    text = "${(progress * 100).toInt()}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
