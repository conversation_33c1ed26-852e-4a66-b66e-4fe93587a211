com.measure.ar.MainActivitycom.measure.ar.ar.AREnginecom.measure.ar.data.Point3D#com.measure.ar.data.MeasurementType&com.measure.ar.data.MeasurementQualitycom.measure.ar.data.Measurement&com.measure.ar.data.MeasurementSession#com.measure.ar.data.ARTrackingState"com.measure.ar.data.ARSessionState$com.measure.ar.data.CameraIntrinsics,com.measure.ar.ui.components.ARGLSurfaceView'com.measure.ar.ui.components.ARRenderer2com.measure.ar.ui.components.DisplayRotationHelper/com.measure.ar.ui.components.BackgroundRenderer*com.measure.ar.ui.components.PlaneRenderer/com.measure.ar.ui.components.PointCloudRenderercom.measure.ar.utils.MathUtils"com.measure.ar.utils.UnitConverter$com.measure.ar.utils.MeasurementTypecom.measure.ar.utils.UnitSystem)com.measure.ar.viewmodel.MeasureViewModel'com.measure.ar.viewmodel.MeasureUiState                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   