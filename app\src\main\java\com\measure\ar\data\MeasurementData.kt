package com.measure.ar.data

import androidx.compose.runtime.Immutable
import kotlin.math.sqrt

/**
 * Represents a 3D point in AR space
 */
@Immutable
data class Point3D(
    val x: Float,
    val y: Float,
    val z: Float
) {
    fun distanceTo(other: Point3D): Float {
        val dx = x - other.x
        val dy = y - other.y
        val dz = z - other.z
        return sqrt(dx * dx + dy * dy + dz * dz)
    }
    
    fun toArray(): FloatArray = floatArrayOf(x, y, z)
}

/**
 * Measurement types supported by the app
 */
enum class MeasurementType {
    SINGLE_LINE,
    MULTI_SEGMENT,
    AREA,
    VOLUME,
    LEVEL
}

/**
 * Quality indicator for measurement accuracy
 */
enum class MeasurementQuality {
    POOR,
    FAIR,
    GOOD,
    EXCELLENT
}

/**
 * Represents a single measurement with metadata
 */
@Immutable
data class Measurement(
    val id: String,
    val type: MeasurementType,
    val points: List<Point3D>,
    val value: Float, // in meters
    val unit: String = "cm",
    val quality: MeasurementQuality,
    val confidence: Float, // 0.0 to 1.0
    val timestamp: Long = System.currentTimeMillis(),
    val notes: String = ""
) {
    val displayValue: String
        get() = when (unit) {
            "cm" -> "%.1f cm".format(value * 100)
            "m" -> "%.3f m".format(value)
            "in" -> "%.2f in".format(value * 39.3701)
            "ft" -> "%.2f ft".format(value * 3.28084)
            else -> "%.3f m".format(value)
        }
    
    val confidenceRange: String
        get() = "± %.1f cm".format((1.0f - confidence) * value * 100)
}

/**
 * Represents a measurement session with multiple measurements
 */
@Immutable
data class MeasurementSession(
    val id: String,
    val measurements: List<Measurement> = emptyList(),
    val calibrationQuality: MeasurementQuality = MeasurementQuality.POOR,
    val environmentLighting: Float = 0.5f, // 0.0 to 1.0
    val surfaceTexture: Float = 0.5f, // 0.0 to 1.0
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * AR tracking state
 */
enum class ARTrackingState {
    NOT_TRACKING,
    LIMITED,
    TRACKING
}

/**
 * Current AR session state
 */
@Immutable
data class ARSessionState(
    val isInitialized: Boolean = false,
    val trackingState: ARTrackingState = ARTrackingState.NOT_TRACKING,
    val planeDetectionEnabled: Boolean = true,
    val depthEnabled: Boolean = false,
    val lightEstimation: Float = 0.5f,
    val cameraIntrinsics: CameraIntrinsics? = null
)

/**
 * Camera calibration data
 */
@Immutable
data class CameraIntrinsics(
    val focalLengthX: Float,
    val focalLengthY: Float,
    val principalPointX: Float,
    val principalPointY: Float,
    val imageWidth: Int,
    val imageHeight: Int
)
