{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3363,3478,3594,3707,4749,4827,4917,5025,5153,5265,5405,5485,5580,5672,5767,5888,6002,6102,6237,6368,6503,6695,6821,6935,7058,7182,7275,7372,7490,7615,7709,7808,7911,8044,8188,8293,8595,8770,9500,9742,9929,10321,10404,10487,10583,10688,10780,10875,10959,11066,11158,11253,11387,11467,11566", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "3473,3589,3702,3816,4822,4912,5020,5148,5260,5400,5480,5575,5667,5762,5883,5997,6097,6232,6363,6498,6690,6816,6930,7053,7177,7270,7367,7485,7610,7704,7803,7906,8039,8183,8288,8387,8670,8843,9579,9823,10031,10399,10482,10578,10683,10775,10870,10954,11061,11153,11248,11382,11462,11561,11654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,913,1007,1102,1199,1295,1399,1495,1593,1689,1783,1877,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,80,93,94,96,95,103,95,97,95,93,93,81,108,107,99,109,104,105,175,100,82", "endOffsets": "216,320,433,520,622,744,827,908,1002,1097,1194,1290,1394,1490,1588,1684,1778,1872,1954,2063,2171,2271,2381,2486,2592,2768,2869,2952"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "594,710,814,927,1014,1116,1238,1321,1402,1496,1591,1688,1784,1888,1984,2082,2178,2272,2366,2448,2557,2665,2765,2875,2980,3086,3262,9584", "endColumns": "115,103,112,86,101,121,82,80,93,94,96,95,103,95,97,95,93,93,81,108,107,99,109,104,105,175,100,82", "endOffsets": "705,809,922,1009,1111,1233,1316,1397,1491,1586,1683,1779,1883,1979,2077,2173,2267,2361,2443,2552,2660,2760,2870,2975,3081,3257,3358,9662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3821,3919,4029,4128,4231,4342,4452,9828", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3914,4024,4123,4226,4337,4447,4567,9924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,287,425,567", "endColumns": "48,47,137,141,91", "endOffsets": "238,286,424,566,658"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,158,210,352,498", "endColumns": "52,51,141,145,95", "endOffsets": "153,205,347,493,589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4572,4665,8392,8490,8675,8848,8925,9016,9103,9187,9257,9326,9412,9667,10036,10116,10199", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "4660,4744,8485,8590,8765,8920,9011,9098,9182,9252,9321,9407,9495,9737,10111,10194,10316"}}]}]}