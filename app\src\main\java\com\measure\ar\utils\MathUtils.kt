package com.measure.ar.utils

import com.measure.ar.data.Point3D
import kotlin.math.*

/**
 * Mathematical utilities for measurement calculations
 */
object MathUtils {
    
    /**
     * Calculate the distance between two 3D points
     */
    fun distance3D(p1: Point3D, p2: Point3D): Float {
        val dx = p1.x - p2.x
        val dy = p1.y - p2.y
        val dz = p1.z - p2.z
        return sqrt(dx * dx + dy * dy + dz * dz)
    }
    
    /**
     * Calculate the area of a polygon using the shoelace formula
     * Assumes points are in a 2D plane (uses x and z coordinates)
     */
    fun calculatePolygonArea(points: List<Point3D>): Float {
        if (points.size < 3) return 0f
        
        var area = 0f
        val n = points.size
        
        for (i in 0 until n) {
            val j = (i + 1) % n
            area += points[i].x * points[j].z - points[j].x * points[i].z
        }
        
        return abs(area) / 2f
    }
    
    /**
     * Calculate the volume of a convex hull formed by points
     * Simplified calculation assuming a roughly rectangular prism
     */
    fun calculateVolume(points: List<Point3D>): Float {
        if (points.size < 4) return 0f
        
        // Find bounding box
        val minX = points.minOf { it.x }
        val maxX = points.maxOf { it.x }
        val minY = points.minOf { it.y }
        val maxY = points.maxOf { it.y }
        val minZ = points.minOf { it.z }
        val maxZ = points.maxOf { it.z }
        
        val width = maxX - minX
        val height = maxY - minY
        val depth = maxZ - minZ
        
        return width * height * depth
    }
    
    /**
     * Calculate the angle between two vectors
     */
    fun angleBetweenVectors(v1: Point3D, v2: Point3D): Float {
        val dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z
        val mag1 = sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z)
        val mag2 = sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z)
        
        if (mag1 == 0f || mag2 == 0f) return 0f
        
        val cosAngle = dot / (mag1 * mag2)
        return acos(cosAngle.coerceIn(-1f, 1f))
    }
    
    /**
     * Convert degrees to radians
     */
    fun degreesToRadians(degrees: Float): Float = degrees * PI.toFloat() / 180f
    
    /**
     * Convert radians to degrees
     */
    fun radiansToDegrees(radians: Float): Float = radians * 180f / PI.toFloat()
    
    /**
     * Linear interpolation between two values
     */
    fun lerp(start: Float, end: Float, t: Float): Float {
        return start + t * (end - start)
    }
    
    /**
     * Clamp a value between min and max
     */
    fun clamp(value: Float, min: Float, max: Float): Float {
        return when {
            value < min -> min
            value > max -> max
            else -> value
        }
    }
    
    /**
     * Calculate the centroid of a set of points
     */
    fun calculateCentroid(points: List<Point3D>): Point3D {
        if (points.isEmpty()) return Point3D(0f, 0f, 0f)
        
        val sumX = points.sumOf { it.x.toDouble() }.toFloat()
        val sumY = points.sumOf { it.y.toDouble() }.toFloat()
        val sumZ = points.sumOf { it.z.toDouble() }.toFloat()
        
        return Point3D(
            sumX / points.size,
            sumY / points.size,
            sumZ / points.size
        )
    }
    
    /**
     * Check if a point is inside a polygon (2D check using x,z coordinates)
     */
    fun isPointInPolygon(point: Point3D, polygon: List<Point3D>): Boolean {
        if (polygon.size < 3) return false
        
        var inside = false
        var j = polygon.size - 1
        
        for (i in polygon.indices) {
            val xi = polygon[i].x
            val zi = polygon[i].z
            val xj = polygon[j].x
            val zj = polygon[j].z
            
            if (((zi > point.z) != (zj > point.z)) &&
                (point.x < (xj - xi) * (point.z - zi) / (zj - zi) + xi)) {
                inside = !inside
            }
            j = i
        }
        
        return inside
    }
    
    /**
     * Calculate the perimeter of a polygon
     */
    fun calculatePerimeter(points: List<Point3D>): Float {
        if (points.size < 2) return 0f
        
        var perimeter = 0f
        for (i in 0 until points.size - 1) {
            perimeter += distance3D(points[i], points[i + 1])
        }
        
        // Close the polygon
        if (points.size > 2) {
            perimeter += distance3D(points.last(), points.first())
        }
        
        return perimeter
    }
    
    /**
     * Smooth a path using moving average
     */
    fun smoothPath(points: List<Point3D>, windowSize: Int = 3): List<Point3D> {
        if (points.size <= windowSize) return points
        
        val smoothed = mutableListOf<Point3D>()
        val halfWindow = windowSize / 2
        
        for (i in points.indices) {
            val start = maxOf(0, i - halfWindow)
            val end = minOf(points.size - 1, i + halfWindow)
            
            var sumX = 0f
            var sumY = 0f
            var sumZ = 0f
            var count = 0
            
            for (j in start..end) {
                sumX += points[j].x
                sumY += points[j].y
                sumZ += points[j].z
                count++
            }
            
            smoothed.add(Point3D(sumX / count, sumY / count, sumZ / count))
        }
        
        return smoothed
    }
}
