{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "552,672,775,891,977,1082,1201,1281,1358,1450,1544,1639,1733,1828,1922,2018,2113,2205,2297,2378,2484,2589,2687,2795,2901,3009,3182,9496", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "667,770,886,972,1077,1196,1276,1353,1445,1539,1634,1728,1823,1917,2013,2108,2200,2292,2373,2479,2584,2682,2790,2896,3004,3177,3277,9573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,394,507,584,677,788,907,1018,1159,1239,1346,1435,1528,1638,1757,1863,2006,2148,2282,2455,2583,2704,2830,2949,3039,3133,3255,3384,3479,3589,3694,3839,3988,4092,4187,4268,4346,4428,4511,4607,4690,4773,4869,4971,5063,5157,5242,5346,5438,5533,5675,5761,5876", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "163,274,389,502,579,672,783,902,1013,1154,1234,1341,1430,1523,1633,1752,1858,2001,2143,2277,2450,2578,2699,2825,2944,3034,3128,3250,3379,3474,3584,3689,3834,3983,4087,4182,4263,4341,4423,4506,4602,4685,4768,4864,4966,5058,5152,5237,5341,5433,5528,5670,5756,5871,5963"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3282,3395,3506,3621,4642,4719,4812,4923,5042,5153,5294,5374,5481,5570,5663,5773,5892,5998,6141,6283,6417,6590,6718,6839,6965,7084,7174,7268,7390,7519,7614,7724,7829,7974,8123,8227,8519,8686,9414,9650,9834,10208,10291,10374,10470,10572,10664,10758,10843,10947,11039,11134,11276,11362,11477", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "3390,3501,3616,3729,4714,4807,4918,5037,5148,5289,5369,5476,5565,5658,5768,5887,5993,6136,6278,6412,6585,6713,6834,6960,7079,7169,7263,7385,7514,7609,7719,7824,7969,8118,8222,8317,8595,8759,9491,9728,9925,10286,10369,10465,10567,10659,10753,10838,10942,11034,11129,11271,11357,11472,11564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4465,4558,8322,8416,8600,8764,8844,8933,9021,9103,9174,9244,9327,9578,9930,10015,10085", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "4553,4637,8411,8514,8681,8839,8928,9016,9098,9169,9239,9322,9409,9645,10010,10080,10203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3734,3832,3934,4034,4135,4241,4344,9733", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3827,3929,4029,4130,4236,4339,4460,9829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,288,408,531", "endColumns": "49,47,119,122,85", "endOffsets": "239,287,407,530,616"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,159,211,335,462", "endColumns": "53,51,123,126,89", "endOffsets": "154,206,330,457,547"}}]}]}