{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,280,387,500,578,670,776,901,1011,1146,1226,1328,1415,1504,1614,1738,1845,1969,2091,2220,2389,2509,2622,2741,2859,2947,3038,3153,3270,3367,3466,3566,3692,3824,3927,4019,4091,4171,4253,4338,4428,4507,4586,4681,4777,4865,4962,5046,5149,5247,5354,5471,5549,5653", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "162,275,382,495,573,665,771,896,1006,1141,1221,1323,1410,1499,1609,1733,1840,1964,2086,2215,2384,2504,2617,2736,2854,2942,3033,3148,3265,3362,3461,3561,3687,3819,3922,4014,4086,4166,4248,4333,4423,4502,4581,4676,4772,4860,4957,5041,5144,5242,5349,5466,5544,5648,5743"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3249,3361,3474,3581,4598,4676,4768,4874,4999,5109,5244,5324,5426,5513,5602,5712,5836,5943,6067,6189,6318,6487,6607,6720,6839,6957,7045,7136,7251,7368,7465,7564,7664,7790,7922,8025,8315,8469,9204,9441,9627,9983,10062,10141,10236,10332,10420,10517,10601,10704,10802,10909,11026,11104,11208", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "3356,3469,3576,3689,4671,4763,4869,4994,5104,5239,5319,5421,5508,5597,5707,5831,5938,6062,6184,6313,6482,6602,6715,6834,6952,7040,7131,7246,7363,7460,7559,7659,7785,7917,8020,8112,8382,8544,9281,9521,9712,10057,10136,10231,10327,10415,10512,10596,10699,10797,10904,11021,11099,11203,11298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,243,298,402,513", "endColumns": "52,54,103,110,84", "endOffsets": "242,297,401,512,597"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,162,221,329,444", "endColumns": "56,58,107,114,88", "endOffsets": "157,216,324,439,528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4426,4515,8117,8216,8387,8549,8634,8725,8811,8891,8968,9043,9122,9368,9717,9798,9865", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "4510,4593,8211,8310,8464,8629,8720,8806,8886,8963,9038,9117,9199,9436,9793,9860,9978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "533,649,756,863,946,1051,1167,1257,1343,1434,1527,1621,1715,1815,1908,2003,2097,2188,2279,2363,2472,2576,2674,2784,2884,2991,3150,9286", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "644,751,858,941,1046,1162,1252,1338,1429,1522,1616,1710,1810,1903,1998,2092,2183,2274,2358,2467,2571,2669,2779,2879,2986,3145,3244,9363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3694,3796,3899,4004,4109,4208,4312,9526", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3791,3894,3999,4104,4203,4307,4421,9622"}}]}]}