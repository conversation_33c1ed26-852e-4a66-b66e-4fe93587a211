package com.measure.ar.ui.components

import android.content.Context
import android.opengl.GLES11Ext
import android.opengl.GLES20
import android.opengl.GLSurfaceView
import android.view.MotionEvent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.google.ar.core.*
import com.google.ar.core.exceptions.CameraNotAvailableException
import com.google.ar.core.ArCoreApk
import com.google.ar.core.Coordinates2d
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

/**
 * AR Camera View component that displays the camera feed with AR overlays
 */
@Composable
fun ARCameraView(
    onTap: (Float, Float) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var surfaceView by remember { mutableStateOf<ARGLSurfaceView?>(null) }

    AndroidView(
        factory = { ctx ->
            ARGLSurfaceView(ctx, onTap).also { view ->
                surfaceView = view
            }
        },
        modifier = modifier.fillMaxSize(),
        update = { view ->
            // Ensure the view is properly initialized
            view.onResume()
        }
    )

    // Handle lifecycle
    DisposableEffect(surfaceView) {
        surfaceView?.onResume()
        onDispose {
            surfaceView?.onPause()
        }
    }
}

/**
 * Custom GLSurfaceView for AR rendering
 */
private class ARGLSurfaceView(
    context: Context,
    private val onTap: (Float, Float) -> Unit
) : GLSurfaceView(context) {

    private val renderer = ARRenderer(context)

    init {
        preserveEGLContextOnPause = true
        setEGLContextClientVersion(2)
        setEGLConfigChooser(8, 8, 8, 8, 16, 0)
        setRenderer(renderer)
        renderMode = RENDERMODE_CONTINUOUSLY
        setWillNotDraw(false)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            onTap(event.x, event.y)
            return true
        }
        return super.onTouchEvent(event)
    }

    override fun onResume() {
        super.onResume()
        renderer.onResume()
    }

    override fun onPause() {
        super.onPause()
        renderer.onPause()
    }
}

/**
 * OpenGL renderer for AR content
 */
private class ARRenderer(private val context: Context) : GLSurfaceView.Renderer {

    private var session: Session? = null
    private var displayRotationHelper = DisplayRotationHelper(context)
    private val backgroundRenderer = BackgroundRenderer()
    private val planeRenderer = PlaneRenderer()
    private val pointCloudRenderer = PointCloudRenderer()

    // Shader programs
    private var backgroundShaderProgram = 0
    private var planeShaderProgram = 0

    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        GLES20.glClearColor(0.1f, 0.1f, 0.1f, 1.0f)

        try {
            // Initialize renderers
            backgroundRenderer.createOnGlThread(context)
            planeRenderer.createOnGlThread(context, "models/trigrid.png")
            pointCloudRenderer.createOnGlThread(context)

        } catch (e: Exception) {
            // Handle initialization errors
        }
    }

    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        displayRotationHelper.onSurfaceChanged(width, height)
        GLES20.glViewport(0, 0, width, height)

        // Update background renderer with correct aspect ratio
        backgroundRenderer.updateScreenGeometry(width, height)
    }

    override fun onDrawFrame(gl: GL10?) {
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)

        session?.let { session ->
            displayRotationHelper.updateSessionIfNeeded(session)

            try {
                // Set camera texture
                session.setCameraTextureName(backgroundRenderer.textureId)
                val frame = session.update()
                val camera = frame.camera

                // Always draw background (camera feed)
                backgroundRenderer.draw(frame)

                // Only draw AR content if tracking
                if (camera.trackingState == TrackingState.TRACKING) {
                    // Get projection and view matrices
                    val projectionMatrix = FloatArray(16)
                    camera.getProjectionMatrix(projectionMatrix, 0, 0.1f, 100.0f)

                    val viewMatrix = FloatArray(16)
                    camera.getViewMatrix(viewMatrix, 0)

                    // Draw point cloud for debugging
                    try {
                        val pointCloud = frame.acquirePointCloud()
                        pointCloudRenderer.update(pointCloud)
                        pointCloudRenderer.draw(viewMatrix, projectionMatrix)
                        pointCloud.release()
                    } catch (e: Exception) {
                        // Ignore point cloud errors
                    }

                    // Draw detected planes
                    try {
                        planeRenderer.drawPlanes(
                            session.getAllTrackables(Plane::class.java),
                            camera.displayOrientedPose,
                            projectionMatrix
                        )
                    } catch (e: Exception) {
                        // Ignore plane rendering errors
                    }
                }

            } catch (e: CameraNotAvailableException) {
                // Camera not available - show black screen
            } catch (e: Exception) {
                // Other errors - continue rendering
            }
        }
    }

    fun onResume() {
        if (session == null) {
            try {
                // Check ARCore availability first
                when (ArCoreApk.getInstance().checkAvailability(context)) {
                    ArCoreApk.Availability.SUPPORTED_INSTALLED -> {
                        session = Session(context).apply {
                            val config = Config(this).apply {
                                planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL
                                lightEstimationMode = Config.LightEstimationMode.ENVIRONMENTAL_HDR
                                depthMode = if (isDepthModeSupported(Config.DepthMode.AUTOMATIC)) {
                                    Config.DepthMode.AUTOMATIC
                                } else {
                                    Config.DepthMode.DISABLED
                                }
                                instantPlacementMode = Config.InstantPlacementMode.LOCAL_Y_UP
                            }
                            configure(config)
                        }
                    }
                    else -> {
                        // ARCore not available
                        return
                    }
                }
            } catch (e: Exception) {
                // Handle session creation errors
                session = null
                return
            }
        }

        try {
            session?.resume()
        } catch (e: CameraNotAvailableException) {
            // Handle camera not available
        } catch (e: Exception) {
            // Handle other resume errors
        }
    }

    fun onPause() {
        session?.pause()
    }
}

/**
 * Helper class for handling display rotation
 */
private class DisplayRotationHelper(private val context: Context) {
    private var viewportChanged = false
    private var viewportWidth = 0
    private var viewportHeight = 0

    fun onSurfaceChanged(width: Int, height: Int) {
        viewportWidth = width
        viewportHeight = height
        viewportChanged = true
    }

    fun updateSessionIfNeeded(session: Session) {
        if (viewportChanged) {
            val displayRotation = context.display?.rotation ?: 0
            session.setDisplayGeometry(displayRotation, viewportWidth, viewportHeight)
            viewportChanged = false
        }
    }
}

/**
 * Renderer for camera background
 */
private class BackgroundRenderer {
    var textureId = -1
        private set

    private var quadVertexBuffer: java.nio.FloatBuffer? = null
    private var quadTexCoordBuffer: java.nio.FloatBuffer? = null
    private var quadProgram = 0
    private var quadPositionParam = 0
    private var quadTexCoordParam = 0
    private var textureParam = 0
    private var screenWidth = 0
    private var screenHeight = 0

    fun createOnGlThread(context: Context) {
        // Create external texture for camera
        val textures = IntArray(1)
        GLES20.glGenTextures(1, textures, 0)
        textureId = textures[0]

        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId)
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE)
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE)
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_LINEAR)
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR)

        // Create shader program
        val vertexShader = """
            attribute vec4 a_Position;
            attribute vec2 a_TexCoord;
            varying vec2 v_TexCoord;
            void main() {
                gl_Position = a_Position;
                v_TexCoord = a_TexCoord;
            }
        """.trimIndent()

        val fragmentShader = """
            #extension GL_OES_EGL_image_external : require
            precision mediump float;
            varying vec2 v_TexCoord;
            uniform samplerExternalOES u_Texture;
            void main() {
                gl_FragColor = texture2D(u_Texture, v_TexCoord);
            }
        """.trimIndent()

        quadProgram = createShaderProgram(vertexShader, fragmentShader)
        quadPositionParam = GLES20.glGetAttribLocation(quadProgram, "a_Position")
        quadTexCoordParam = GLES20.glGetAttribLocation(quadProgram, "a_TexCoord")
        textureParam = GLES20.glGetUniformLocation(quadProgram, "u_Texture")

        // Create quad vertices
        val quadVertices = floatArrayOf(
            -1.0f, -1.0f, 0.0f,
             1.0f, -1.0f, 0.0f,
            -1.0f,  1.0f, 0.0f,
             1.0f,  1.0f, 0.0f
        )

        val quadTexCoords = floatArrayOf(
            0.0f, 1.0f,
            1.0f, 1.0f,
            0.0f, 0.0f,
            1.0f, 0.0f
        )

        quadVertexBuffer = java.nio.ByteBuffer.allocateDirect(quadVertices.size * 4)
            .order(java.nio.ByteOrder.nativeOrder())
            .asFloatBuffer()
            .put(quadVertices)
            .apply { position(0) }

        quadTexCoordBuffer = java.nio.ByteBuffer.allocateDirect(quadTexCoords.size * 4)
            .order(java.nio.ByteOrder.nativeOrder())
            .asFloatBuffer()
            .put(quadTexCoords)
            .apply { position(0) }
    }

    fun updateScreenGeometry(width: Int, height: Int) {
        screenWidth = width
        screenHeight = height
    }

    fun draw(frame: Frame) {
        if (quadProgram == 0) return

        try {
            // Update texture coordinates if display geometry changed
            if (frame.hasDisplayGeometryChanged()) {
                // Use the newer API method
                frame.transformCoordinates2d(
                    Coordinates2d.OPENGL_NORMALIZED_DEVICE_COORDINATES,
                    quadTexCoordBuffer,
                    Coordinates2d.TEXTURE_NORMALIZED,
                    quadTexCoordBuffer
                )
            }

            GLES20.glUseProgram(quadProgram)
            GLES20.glDepthMask(false)

            // Bind texture
            GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId)
            GLES20.glUniform1i(textureParam, 0)

            // Draw quad
            GLES20.glVertexAttribPointer(quadPositionParam, 3, GLES20.GL_FLOAT, false, 0, quadVertexBuffer)
            GLES20.glVertexAttribPointer(quadTexCoordParam, 2, GLES20.GL_FLOAT, false, 0, quadTexCoordBuffer)
            GLES20.glEnableVertexAttribArray(quadPositionParam)
            GLES20.glEnableVertexAttribArray(quadTexCoordParam)

            GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4)

            GLES20.glDisableVertexAttribArray(quadPositionParam)
            GLES20.glDisableVertexAttribArray(quadTexCoordParam)
            GLES20.glDepthMask(true)
        } catch (e: Exception) {
            // If external texture fails, just clear to show something
            GLES20.glClearColor(0.1f, 0.1f, 0.1f, 1.0f)
        }
    }

    private fun createShaderProgram(vertexSource: String, fragmentSource: String): Int {
        return try {
            val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexSource)
            val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentSource)

            if (vertexShader == 0 || fragmentShader == 0) return 0

            val program = GLES20.glCreateProgram()
            GLES20.glAttachShader(program, vertexShader)
            GLES20.glAttachShader(program, fragmentShader)
            GLES20.glLinkProgram(program)

            // Check link status
            val linkStatus = IntArray(1)
            GLES20.glGetProgramiv(program, GLES20.GL_LINK_STATUS, linkStatus, 0)
            if (linkStatus[0] == 0) {
                GLES20.glDeleteProgram(program)
                return 0
            }

            program
        } catch (e: Exception) {
            0
        }
    }

    private fun loadShader(type: Int, source: String): Int {
        return try {
            val shader = GLES20.glCreateShader(type)
            GLES20.glShaderSource(shader, source)
            GLES20.glCompileShader(shader)

            // Check compile status
            val compileStatus = IntArray(1)
            GLES20.glGetShaderiv(shader, GLES20.GL_COMPILE_STATUS, compileStatus, 0)
            if (compileStatus[0] == 0) {
                GLES20.glDeleteShader(shader)
                return 0
            }

            shader
        } catch (e: Exception) {
            0
        }
    }
}

/**
 * Renderer for detected planes
 */
private class PlaneRenderer {
    fun createOnGlThread(context: Context, gridTexture: String) {
        // Initialize plane rendering
    }

    fun drawPlanes(
        allPlanes: Collection<Plane>,
        cameraPose: Pose,
        projectionMatrix: FloatArray
    ) {
        // Draw detected planes
        for (plane in allPlanes) {
            if (plane.trackingState != TrackingState.TRACKING || plane.subsumedBy != null) {
                continue
            }

            // Render plane visualization
        }
    }
}

/**
 * Renderer for point cloud
 */
private class PointCloudRenderer {
    fun createOnGlThread(context: Context) {
        // Initialize point cloud rendering
    }

    fun update(pointCloud: PointCloud) {
        // Update point cloud data
    }

    fun draw(viewMatrix: FloatArray, projectionMatrix: FloatArray) {
        // Draw point cloud
    }
}
