com.measure.ar.MainActivitycom.measure.ar.ar.AREngine#com.measure.ar.data.MeasurementType&com.measure.ar.data.MeasurementQuality#com.measure.ar.data.ARTrackingState,com.measure.ar.ui.components.ARGLSurfaceView'com.measure.ar.ui.components.ARRenderer$com.measure.ar.utils.MeasurementTypecom.measure.ar.utils.UnitSystem)com.measure.ar.viewmodel.MeasureViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      