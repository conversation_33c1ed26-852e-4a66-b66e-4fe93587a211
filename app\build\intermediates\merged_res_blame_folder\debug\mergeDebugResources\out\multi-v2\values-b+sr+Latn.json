{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3721,3819,3921,4018,4122,4226,4331,9679", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3814,3916,4013,4117,4221,4326,4442,9775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "533,640,741,847,933,1037,1159,1244,1326,1417,1510,1605,1699,1799,1892,1987,2092,2183,2274,2360,2465,2571,2674,2781,2890,2997,3167,9435", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "635,736,842,928,1032,1154,1239,1321,1412,1505,1600,1694,1794,1887,1982,2087,2178,2269,2355,2460,2566,2669,2776,2885,2992,3162,3259,9517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4447,4544,8260,8357,8531,8697,8774,8865,8957,9042,9114,9185,9265,9522,9883,9962,10032", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "4539,4626,8352,8453,8612,8769,8860,8952,9037,9109,9180,9260,9345,9590,9957,10027,10145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,401,512,593,683,791,923,1039,1179,1260,1356,1447,1541,1653,1774,1875,2012,2148,2277,2453,2574,2690,2812,2931,3023,3117,3231,3357,3453,3551,3656,3793,3938,4043,4141,4214,4294,4379,4463,4566,4642,4721,4814,4913,5002,5096,5179,5283,5376,5473,5602,5678,5781", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "161,272,396,507,588,678,786,918,1034,1174,1255,1351,1442,1536,1648,1769,1870,2007,2143,2272,2448,2569,2685,2807,2926,3018,3112,3226,3352,3448,3546,3651,3788,3933,4038,4136,4209,4289,4374,4458,4561,4637,4716,4809,4908,4997,5091,5174,5278,5371,5468,5597,5673,5776,5871"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3264,3375,3486,3610,4631,4712,4802,4910,5042,5158,5298,5379,5475,5566,5660,5772,5893,5994,6131,6267,6396,6572,6693,6809,6931,7050,7142,7236,7350,7476,7572,7670,7775,7912,8057,8162,8458,8617,9350,9595,9780,10150,10226,10305,10398,10497,10586,10680,10763,10867,10960,11057,11186,11262,11365", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "3370,3481,3605,3716,4707,4797,4905,5037,5153,5293,5374,5470,5561,5655,5767,5888,5989,6126,6262,6391,6567,6688,6804,6926,7045,7137,7231,7345,7471,7567,7665,7770,7907,8052,8157,8255,8526,8692,9430,9674,9878,10221,10300,10393,10492,10581,10675,10758,10862,10955,11052,11181,11257,11360,11455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "197,244,294,406,520", "endColumns": "46,49,111,113,84", "endOffsets": "243,293,405,519,604"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,210,326,444", "endColumns": "50,53,115,117,88", "endOffsets": "151,205,321,439,528"}}]}]}