1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.measure.ar"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- ARCore permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:7:5-67
13-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:8:5-79
14-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:8:22-76
15
16    <!-- Sensor permissions -->
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:11:5-66
17-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:11:22-63
18
19    <!-- Storage for exporting measurements -->
20    <uses-permission
20-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:14:5-15:38
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:14:22-78
22        android:maxSdkVersion="28" />
22-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:15:9-35
23    <uses-permission
23-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:16:5-17:38
24        android:name="android.permission.READ_EXTERNAL_STORAGE"
24-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:16:22-77
25        android:maxSdkVersion="32" />
25-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:17:9-35
26
27    <!-- Required hardware features -->
28    <uses-feature
28-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:5-87
29        android:name="android.hardware.camera.ar"
29-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:19-60
30        android:required="true" />
30-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:20:61-84
31    <uses-feature
31-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:5-98
32        android:name="android.hardware.sensor.accelerometer"
32-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:19-71
33        android:required="true" />
33-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:21:72-95
34    <uses-feature
34-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:5-94
35        android:name="android.hardware.sensor.gyroscope"
35-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:19-67
36        android:required="true" />
36-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:22:68-91
37
38    <permission
38-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.measure.ar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.measure.ar.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
43
44    <queries>
44-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:20:5-22:15
45        <package android:name="com.google.ar.core" />
45-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:21:9-54
45-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:21:18-51
46    </queries>
47
48    <application
48-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:24:5-50:19
49        android:allowBackup="true"
49-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:25:9-35
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68bbd61117eb14a617b740563c343275\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
51        android:dataExtractionRules="@xml/data_extraction_rules"
51-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:26:9-65
52        android:debuggable="true"
53        android:extractNativeLibs="false"
54        android:fullBackupContent="@xml/backup_rules"
54-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:27:9-54
55        android:icon="@drawable/ic_launcher_placeholder"
55-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:28:9-57
56        android:label="@string/app_name"
56-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:29:9-41
57        android:roundIcon="@drawable/ic_launcher_placeholder"
57-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:30:9-62
58        android:supportsRtl="true"
58-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:31:9-35
59        android:testOnly="true"
60        android:theme="@style/Theme.Measure" >
60-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:32:9-45
61
62        <!-- ARCore metadata -->
63        <meta-data
63-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:9-81
64            android:name="com.google.ar.core"
64-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:20-53
65            android:value="required" />
65-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:36:54-78
66
67        <activity
67-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:38:9-49:20
68            android:name="com.measure.ar.MainActivity"
68-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:39:13-41
69            android:configChanges="orientation|screenSize|keyboardHidden"
69-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:44:13-74
70            android:exported="true"
70-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:40:13-36
71            android:label="@string/app_name"
71-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:41:13-45
72            android:screenOrientation="portrait"
72-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:43:13-49
73            android:theme="@style/Theme.Measure" >
73-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:42:13-49
74            <intent-filter>
74-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:45:13-48:29
75                <action android:name="android.intent.action.MAIN" />
75-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:46:17-69
75-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:46:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:47:17-77
77-->C:\Users\<USER>\OneDrive\Desktop\Measure Claude\app\src\main\AndroidManifest.xml:47:27-74
78            </intent-filter>
79        </activity>
80        <activity
80-->[androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:23:9-25:39
81            android:name="androidx.activity.ComponentActivity"
81-->[androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:24:13-63
82            android:exported="true" />
82-->[androidx.compose.ui:ui-test-manifest:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92d3b930b5b1cd76820fbab5821558c3\transformed\ui-test-manifest-1.4.3\AndroidManifest.xml:25:13-36
83        <activity
83-->[androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:23:9-25:39
84            android:name="androidx.compose.ui.tooling.PreviewActivity"
84-->[androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:24:13-71
85            android:exported="true" />
85-->[androidx.compose.ui:ui-tooling:1.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\592d61fa6f51ca06e74909a13a858413\transformed\ui-tooling-1.4.3\AndroidManifest.xml:25:13-36
86
87        <service
87-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
88            android:name="androidx.camera.core.impl.MetadataHolderService"
88-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
89            android:enabled="false"
89-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
90            android:exported="false" >
90-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
91            <meta-data
91-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
92                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
92-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
93                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
93-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4861c922629bdff1be68169278d0c3cf\transformed\camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
94        </service>
95
96        <provider
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
97            android:name="androidx.startup.InitializationProvider"
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
98            android:authorities="com.measure.ar.androidx-startup"
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
99            android:exported="false" >
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
100            <meta-data
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.emoji2.text.EmojiCompatInitializer"
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
102                android:value="androidx.startup" />
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\621f3adc75c9ee97293f7c831a1c19e2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
104-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
105                android:value="androidx.startup" />
105-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e92ca64cc5786742384232535c697d2\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
108                android:value="androidx.startup" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
109        </provider> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
110        <meta-data
110-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:27:9-29:41
111            android:name="com.google.ar.core.min_apk_version"
111-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:28:13-62
112            android:value="231000000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
112-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:29:13-38
113        <activity
113-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:31:9-37:80
114            android:name="com.google.ar.core.InstallActivity"
114-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:32:13-62
115            android:configChanges="keyboardHidden|orientation|screenSize"
115-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:33:13-74
116            android:excludeFromRecents="true"
116-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:34:13-46
117            android:exported="false"
117-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:35:13-37
118            android:launchMode="singleTop"
118-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:36:13-43
119            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />
119-->[com.google.ar:core:1.39.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ccc457f66da22ed7e24b463509a2d4a\transformed\core-1.39.0\AndroidManifest.xml:37:13-77
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932a6fb7a064a337d5202bd568d91c59\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140    </application>
141
142</manifest>
