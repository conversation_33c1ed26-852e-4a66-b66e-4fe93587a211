{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.measure.ar.app-mergeDebugResources-56:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}}]}, {"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,20,21,22,23,24,25,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "981,1051,1135,1219,1315,1417,1519,1675", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1046,1130,1214,1310,1412,1514,1608,1759"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Measure Claude\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "3,21", "startColumns": "4,4", "startOffsets": "153,1120", "endLines": "19,21", "endColumns": "12,62", "endOffsets": "1114,1178"}, "to": {"startLines": "2,26", "startColumns": "4,4", "startOffsets": "55,1613", "endLines": "18,26", "endColumns": "12,61", "endOffsets": "976,1670"}}]}]}