{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,403,515,597,689,799,933,1049,1199,1280,1379,1466,1559,1680,1796,1900,2041,2183,2309,2476,2599,2709,2824,2947,3035,3126,3250,3372,3467,3565,3673,3814,3962,4072,4167,4239,4320,4402,4488,4589,4665,4745,4841,4937,5028,5126,5208,5306,5400,5499,5610,5686,5782", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "164,278,398,510,592,684,794,928,1044,1194,1275,1374,1461,1554,1675,1791,1895,2036,2178,2304,2471,2594,2704,2819,2942,3030,3121,3245,3367,3462,3560,3668,3809,3957,4067,4162,4234,4315,4397,4483,4584,4660,4740,4836,4932,5023,5121,5203,5301,5395,5494,5605,5681,5777,5867"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3241,3355,3469,3589,4632,4714,4806,4916,5050,5166,5316,5397,5496,5583,5676,5797,5913,6017,6158,6300,6426,6593,6716,6826,6941,7064,7152,7243,7367,7489,7584,7682,7790,7931,8079,8189,8475,8636,9369,9605,9792,10160,10236,10316,10412,10508,10599,10697,10779,10877,10971,11070,11181,11257,11353", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "3350,3464,3584,3696,4709,4801,4911,5045,5161,5311,5392,5491,5578,5671,5792,5908,6012,6153,6295,6421,6588,6711,6821,6936,7059,7147,7238,7362,7484,7579,7677,7785,7926,8074,8184,8279,8542,8712,9446,9686,9888,10231,10311,10407,10503,10594,10692,10774,10872,10966,11065,11176,11252,11348,11438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3701,3799,3902,4007,4108,4221,4327,9691", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3794,3897,4002,4103,4216,4322,4449,9787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,2863"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "559,665,763,873,959,1061,1182,1260,1338,1429,1521,1616,1710,1811,1904,1999,2093,2184,2275,2355,2460,2562,2660,2770,2873,2982,3140,9451", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "660,758,868,954,1056,1177,1255,1333,1424,1516,1611,1705,1806,1899,1994,2088,2179,2270,2350,2455,2557,2655,2765,2868,2977,3135,3236,9527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,291,409,528", "endColumns": "48,51,117,118,95", "endOffsets": "238,290,408,527,623"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,158,214,336,459", "endColumns": "52,55,121,122,99", "endOffsets": "153,209,331,454,554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4454,4549,8284,8377,8547,8717,8795,8892,8981,9066,9134,9203,9284,9532,9893,9974,10040", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "4544,4627,8372,8470,8631,8790,8887,8976,9061,9129,9198,9279,9364,9600,9969,10035,10155"}}]}]}