<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.Measure" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/measure_blue</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorPrimaryContainer">@color/measure_blue_dark</item>
        <item name="colorOnPrimaryContainer">@color/white</item>
        
        <item name="colorSecondary">@color/measure_green</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorSecondaryContainer">@color/measure_green_dark</item>
        <item name="colorOnSecondaryContainer">@color/white</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/>
    <style name="Theme.Measure" parent="Base.Theme.Measure"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Dark"/>
</resources>