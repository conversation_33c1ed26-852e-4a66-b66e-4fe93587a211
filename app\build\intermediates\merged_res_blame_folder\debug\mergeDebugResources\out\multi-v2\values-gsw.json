{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-gsw/values-gsw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values-gsw\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "191,241,290,424,561", "endColumns": "49,48,133,136,90", "endOffsets": "240,289,423,560,651"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,109,162,300,441", "endColumns": "53,52,137,140,94", "endOffsets": "104,157,295,436,531"}}]}]}