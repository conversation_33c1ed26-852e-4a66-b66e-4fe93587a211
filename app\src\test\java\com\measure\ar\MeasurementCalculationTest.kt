package com.measure.ar

import com.measure.ar.data.Point3D
import com.measure.ar.utils.MathUtils
import com.measure.ar.utils.UnitConverter
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for measurement calculations
 */
class MeasurementCalculationTest {

    @Test
    fun `distance calculation is correct`() {
        val point1 = Point3D(0f, 0f, 0f)
        val point2 = Point3D(3f, 4f, 0f)
        val distance = point1.distanceTo(point2)
        
        assertEquals(5.0f, distance, 0.001f)
    }

    @Test
    fun `area calculation for rectangle is correct`() {
        val points = listOf(
            Point3D(0f, 0f, 0f),
            Point3D(2f, 0f, 0f),
            Point3D(2f, 0f, 3f),
            Point3D(0f, 0f, 3f)
        )
        val area = MathUtils.calculatePolygonArea(points)
        
        assertEquals(6.0f, area, 0.001f)
    }

    @Test
    fun `unit conversion from meters to centimeters`() {
        val meters = 1.5f
        val centimeters = UnitConverter.convertLength(meters, "m", "cm")
        
        assertEquals(150.0f, centimeters, 0.001f)
    }

    @Test
    fun `unit conversion from inches to centimeters`() {
        val inches = 12f
        val centimeters = UnitConverter.convertLength(inches, "in", "cm")
        
        assertEquals(30.48f, centimeters, 0.01f)
    }

    @Test
    fun `volume calculation for box`() {
        val points = listOf(
            Point3D(0f, 0f, 0f),
            Point3D(2f, 0f, 0f),
            Point3D(2f, 3f, 0f),
            Point3D(0f, 3f, 0f),
            Point3D(0f, 0f, 1f),
            Point3D(2f, 0f, 1f),
            Point3D(2f, 3f, 1f),
            Point3D(0f, 3f, 1f)
        )
        val volume = MathUtils.calculateVolume(points)
        
        assertEquals(6.0f, volume, 0.001f)
    }

    @Test
    fun `centroid calculation is correct`() {
        val points = listOf(
            Point3D(0f, 0f, 0f),
            Point3D(2f, 0f, 0f),
            Point3D(1f, 2f, 0f)
        )
        val centroid = MathUtils.calculateCentroid(points)
        
        assertEquals(1.0f, centroid.x, 0.001f)
        assertEquals(0.667f, centroid.y, 0.01f)
        assertEquals(0.0f, centroid.z, 0.001f)
    }

    @Test
    fun `perimeter calculation is correct`() {
        val points = listOf(
            Point3D(0f, 0f, 0f),
            Point3D(3f, 0f, 0f),
            Point3D(3f, 4f, 0f),
            Point3D(0f, 4f, 0f)
        )
        val perimeter = MathUtils.calculatePerimeter(points)
        
        assertEquals(14.0f, perimeter, 0.001f)
    }

    @Test
    fun `angle between vectors calculation`() {
        val v1 = Point3D(1f, 0f, 0f)
        val v2 = Point3D(0f, 1f, 0f)
        val angle = MathUtils.angleBetweenVectors(v1, v2)
        
        assertEquals(Math.PI.toFloat() / 2, angle, 0.001f)
    }

    @Test
    fun `optimal unit selection for small measurements`() {
        val smallValue = 0.005f // 5mm
        val unit = UnitConverter.getOptimalUnit(
            smallValue, 
            UnitConverter.MeasurementType.LENGTH,
            UnitConverter.UnitSystem.METRIC
        )
        
        assertEquals("mm", unit)
    }

    @Test
    fun `optimal unit selection for large measurements`() {
        val largeValue = 2.5f // 2.5m
        val unit = UnitConverter.getOptimalUnit(
            largeValue,
            UnitConverter.MeasurementType.LENGTH,
            UnitConverter.UnitSystem.METRIC
        )
        
        assertEquals("m", unit)
    }

    @Test
    fun `measurement formatting with correct precision`() {
        val value = 123.456f
        val formatted = UnitConverter.formatMeasurement(value, "cm")
        
        assertEquals("123.5 cm", formatted)
    }
}
