<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.Measure" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/measure_blue</item>
        <item name="colorPrimaryDark">@color/measure_blue_dark</item>
        <item name="colorAccent">@color/measure_green</item>

        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="Theme.Measure" parent="Base.Theme.Measure"/>
</resources>