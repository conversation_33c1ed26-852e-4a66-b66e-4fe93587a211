package com.measure.ar.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.measure.ar.data.MeasurementType
import com.measure.ar.ui.components.ARCameraView
import com.measure.ar.ui.components.CalibrationOverlay
import com.measure.ar.ui.components.MeasurementToolbar
import com.measure.ar.ui.components.QualityIndicator
import com.measure.ar.ui.theme.AROverlay
import com.measure.ar.ui.theme.MeasureBlue
import com.measure.ar.viewmodel.MeasureViewModel

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun MeasureScreen(
    viewModel: MeasureViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val arSessionState by viewModel.arSessionState.collectAsStateWithLifecycle()
    val trackingQuality by viewModel.trackingQuality.collectAsStateWithLifecycle()
    val currentMeasurement by viewModel.currentMeasurement.collectAsStateWithLifecycle()
    
    // Camera permission
    val cameraPermissionState = rememberPermissionState(
        android.Manifest.permission.CAMERA
    )
    
    LaunchedEffect(Unit) {
        if (!cameraPermissionState.status.isGranted) {
            cameraPermissionState.launchPermissionRequest()
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        if (cameraPermissionState.status.isGranted && uiState.isARInitialized) {
            // AR Camera View
            ARCameraView(
                onTap = { x, y ->
                    if (uiState.isMeasuring) {
                        viewModel.addMeasurementPoint(x, y)
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
            
            // Calibration overlay
            if (uiState.isCalibrating) {
                CalibrationOverlay(
                    onCalibrationComplete = {
                        viewModel.checkCalibration()
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            // Top status bar
            TopStatusBar(
                trackingQuality = trackingQuality,
                currentMeasurement = currentMeasurement,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )
            
            // Bottom measurement controls
            BottomMeasurementControls(
                uiState = uiState,
                onStartMeasurement = { type ->
                    viewModel.startMeasurement(type)
                },
                onCompleteMeasurement = {
                    viewModel.completeMeasurement()
                },
                onClearMeasurements = {
                    viewModel.clearMeasurements()
                },
                onStartCalibration = {
                    viewModel.startCalibration()
                },
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .padding(16.dp)
            )
            
            // Measurement instructions
            if (uiState.isMeasuring) {
                MeasurementInstructions(
                    measurementType = uiState.currentMeasurementType,
                    pointCount = uiState.measurementPoints.size,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(32.dp)
                )
            }
            
        } else if (!cameraPermissionState.status.isGranted) {
            // Permission request screen
            PermissionRequestScreen(
                onRequestPermission = {
                    cameraPermissionState.launchPermissionRequest()
                }
            )
        } else {
            // Loading screen
            LoadingScreen()
        }
    }
}

@Composable
private fun TopStatusBar(
    trackingQuality: com.measure.ar.data.MeasurementQuality,
    currentMeasurement: com.measure.ar.data.Measurement?,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        // Quality indicator
        QualityIndicator(
            quality = trackingQuality,
            modifier = Modifier
                .background(
                    AROverlay,
                    RoundedCornerShape(20.dp)
                )
                .padding(12.dp)
        )
        
        // Current measurement display
        currentMeasurement?.let { measurement ->
            Card(
                modifier = Modifier
                    .background(
                        AROverlay,
                        RoundedCornerShape(20.dp)
                    ),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = measurement.displayValue,
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = measurement.confidenceRange,
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun BottomMeasurementControls(
    uiState: com.measure.ar.viewmodel.MeasureUiState,
    onStartMeasurement: (MeasurementType) -> Unit,
    onCompleteMeasurement: () -> Unit,
    onClearMeasurements: () -> Unit,
    onStartCalibration: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        if (uiState.isMeasuring) {
            // Measurement in progress controls
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                FloatingActionButton(
                    onClick = onCompleteMeasurement,
                    containerColor = MeasureBlue
                ) {
                    Icon(Icons.Default.Check, contentDescription = "Complete")
                }
                
                FloatingActionButton(
                    onClick = onClearMeasurements,
                    containerColor = MaterialTheme.colorScheme.error
                ) {
                    Icon(Icons.Default.Clear, contentDescription = "Cancel")
                }
            }
        } else {
            // Measurement type selection
            MeasurementToolbar(
                onMeasurementTypeSelected = onStartMeasurement,
                onCalibrate = onStartCalibration,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun MeasurementInstructions(
    measurementType: MeasurementType?,
    pointCount: Int,
    modifier: Modifier = Modifier
) {
    val instructionText = when (measurementType) {
        MeasurementType.SINGLE_LINE -> {
            when (pointCount) {
                0 -> "Tap to set the first point"
                1 -> "Tap to set the second point"
                else -> "Measurement complete"
            }
        }
        MeasurementType.MULTI_SEGMENT -> {
            when (pointCount) {
                0 -> "Tap to start path measurement"
                1 -> "Tap to add more points"
                else -> "Tap to continue or complete measurement"
            }
        }
        MeasurementType.AREA -> {
            when (pointCount) {
                0 -> "Tap to start area measurement"
                1 -> "Tap to add second corner"
                2 -> "Tap to add third corner"
                else -> "Tap to add more corners or complete"
            }
        }
        else -> ""
    }
    
    if (instructionText.isNotEmpty()) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = AROverlay
            )
        ) {
            Text(
                text = instructionText,
                color = Color.White,
                modifier = Modifier.padding(16.dp),
                fontSize = 16.sp
            )
        }
    }
}

@Composable
private fun PermissionRequestScreen(
    onRequestPermission: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            Icons.Default.CameraAlt,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Camera Permission Required",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "This app needs camera access to measure objects using AR",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onRequestPermission,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Grant Permission")
        }
    }
}

@Composable
private fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text("Initializing AR...")
        }
    }
}
