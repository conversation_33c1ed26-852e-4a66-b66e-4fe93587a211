{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "538,643,738,845,931,1035,1154,1239,1321,1412,1505,1600,1694,1794,1887,1982,2077,2168,2259,2345,2449,2561,2662,2767,2881,2983,3152,9454", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "638,733,840,926,1030,1149,1234,1316,1407,1500,1595,1689,1789,1882,1977,2072,2163,2254,2340,2444,2556,2657,2762,2876,2978,3147,3244,9534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,404,521,596,686,794,931,1046,1187,1268,1364,1455,1549,1664,1786,1887,2019,2150,2280,2444,2566,2686,2811,2932,3024,3118,3244,3374,3467,3565,3670,3806,3949,4054,4149,4230,4307,4397,4479,4584,4668,4747,4840,4937,5026,5125,5209,5310,5403,5499,5633,5719,5815", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "166,283,399,516,591,681,789,926,1041,1182,1263,1359,1450,1544,1659,1781,1882,2014,2145,2275,2439,2561,2681,2806,2927,3019,3113,3239,3369,3462,3560,3665,3801,3944,4049,4144,4225,4302,4392,4474,4579,4663,4742,4835,4932,5021,5120,5204,5305,5398,5494,5628,5714,5810,5898"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3249,3365,3482,3598,4633,4708,4798,4906,5043,5158,5299,5380,5476,5567,5661,5776,5898,5999,6131,6262,6392,6556,6678,6798,6923,7044,7136,7230,7356,7486,7579,7677,7782,7918,8061,8166,8454,8625,9364,9612,9795,10167,10251,10330,10423,10520,10609,10708,10792,10893,10986,11082,11216,11302,11398", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "3360,3477,3593,3710,4703,4793,4901,5038,5153,5294,5375,5471,5562,5656,5771,5893,5994,6126,6257,6387,6551,6673,6793,6918,7039,7131,7225,7351,7481,7574,7672,7777,7913,8056,8161,8256,8530,8697,9449,9689,9895,10246,10325,10418,10515,10604,10703,10787,10888,10981,11077,11211,11297,11393,11481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4441,4546,8261,8355,8535,8702,8781,8874,8969,9054,9126,9197,9278,9539,9900,9979,10049", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4541,4628,8350,8449,8620,8776,8869,8964,9049,9121,9192,9273,9359,9607,9974,10044,10162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3715,3813,3920,4017,4116,4220,4324,9694", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3808,3915,4012,4111,4215,4319,4436,9790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,239,289,403,519", "endColumns": "48,49,113,115,83", "endOffsets": "238,288,402,518,602"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,158,212,330,450", "endColumns": "52,53,117,119,87", "endOffsets": "153,207,325,445,533"}}]}]}