{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,243,297,443,596", "endColumns": "52,53,145,152,90", "endOffsets": "242,296,442,595,686"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,162,220,370,527", "endColumns": "56,57,149,156,94", "endOffsets": "157,215,365,522,617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3c15b22be3cd3ef6196522864286995\\transformed\\ui-1.4.3\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4473,4572,8187,8291,8478,8650,8733,8823,8916,8999,9067,9134,9215,9466,9820,9903,9971", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "4567,4652,8286,8393,8569,8728,8818,8911,8994,9062,9129,9210,9293,9535,9898,9966,10083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b179e72bf03cadde01e4099557985561\\transformed\\material3-1.1.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,389,499,577,671,783,912,1017,1152,1232,1327,1417,1511,1621,1738,1843,1964,2083,2209,2373,2494,2611,2732,2850,2941,3035,3148,3270,3370,3476,3579,3697,3821,3930,4029,4109,4185,4269,4351,4448,4524,4604,4700,4800,4892,4987,5071,5175,5271,5369,5504,5580,5692", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "163,274,384,494,572,666,778,907,1012,1147,1227,1322,1412,1506,1616,1733,1838,1959,2078,2204,2368,2489,2606,2727,2845,2936,3030,3143,3265,3365,3471,3574,3692,3816,3925,4024,4104,4180,4264,4346,4443,4519,4599,4695,4795,4887,4982,5066,5170,5266,5364,5499,5575,5687,5786"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3293,3406,3517,3627,4657,4735,4829,4941,5070,5175,5310,5390,5485,5575,5669,5779,5896,6001,6122,6241,6367,6531,6652,6769,6890,7008,7099,7193,7306,7428,7528,7634,7737,7855,7979,8088,8398,8574,9298,9540,9723,10088,10164,10244,10340,10440,10532,10627,10711,10815,10911,11009,11144,11220,11332", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "3401,3512,3622,3732,4730,4824,4936,5065,5170,5305,5385,5480,5570,5664,5774,5891,5996,6117,6236,6362,6526,6647,6764,6885,7003,7094,7188,7301,7423,7523,7629,7732,7850,7974,8083,8182,8473,8645,9377,9617,9815,10159,10239,10335,10435,10527,10622,10706,10810,10906,11004,11139,11215,11327,11426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "622,727,822,922,1004,1104,1221,1306,1384,1475,1568,1663,1757,1851,1944,2039,2134,2225,2317,2401,2511,2617,2717,2825,2931,3033,3194,9382", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "722,817,917,999,1099,1216,1301,1379,1470,1563,1658,1752,1846,1939,2034,2129,2220,2312,2396,2506,2612,2712,2820,2926,3028,3189,3288,9461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c919e02dd627ce1aec5be10fe930459e\\transformed\\core-1.10.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3737,3839,3941,4042,4142,4250,4354,9622", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3834,3936,4037,4137,4245,4349,4468,9718"}}]}]}