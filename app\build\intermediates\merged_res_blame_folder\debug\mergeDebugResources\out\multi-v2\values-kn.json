{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,289,401,522", "endColumns": "45,52,111,120,100", "endOffsets": "235,288,400,521,622"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,155,212,328,453", "endColumns": "49,56,115,124,104", "endOffsets": "150,207,323,448,553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "558,672,784,897,985,1092,1218,1296,1372,1463,1556,1651,1745,1845,1938,2033,2127,2218,2309,2391,2507,2617,2716,2829,2934,3048,3212,9639", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "667,779,892,980,1087,1213,1291,1367,1458,1551,1646,1740,1840,1933,2028,2122,2213,2304,2386,2502,2612,2711,2824,2929,3043,3207,3307,9717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,413,532,614,708,820,973,1099,1247,1329,1431,1528,1634,1746,1875,1982,2117,2248,2377,2561,2681,2795,2913,3037,3135,3228,3346,3480,3582,3687,3789,3923,4064,4167,4271,4343,4425,4508,4593,4700,4776,4856,4952,5056,5152,5249,5332,5441,5539,5639,5756,5832,5938", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "170,290,408,527,609,703,815,968,1094,1242,1324,1426,1523,1629,1741,1870,1977,2112,2243,2372,2556,2676,2790,2908,3032,3130,3223,3341,3475,3577,3682,3784,3918,4059,4162,4266,4338,4420,4503,4588,4695,4771,4851,4947,5051,5147,5244,5327,5436,5534,5634,5751,5827,5933,6026"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3312,3432,3552,3670,4715,4797,4891,5003,5156,5282,5430,5512,5614,5711,5817,5929,6058,6165,6300,6431,6560,6744,6864,6978,7096,7220,7318,7411,7529,7663,7765,7870,7972,8106,8247,8350,8650,8811,9556,9801,9987,10358,10434,10514,10610,10714,10810,10907,10990,11099,11197,11297,11414,11490,11596", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "3427,3547,3665,3784,4792,4886,4998,5151,5277,5425,5507,5609,5706,5812,5924,6053,6160,6295,6426,6555,6739,6859,6973,7091,7215,7313,7406,7524,7658,7760,7865,7967,8101,8242,8345,8449,8717,8888,9634,9881,10089,10429,10509,10605,10709,10805,10902,10985,11094,11192,11292,11409,11485,11591,11684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3789,3887,3990,4091,4197,4298,4406,9886", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3882,3985,4086,4192,4293,4401,4529,9982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4534,4631,8454,8550,8722,8893,8977,9070,9161,9246,9317,9388,9470,9722,10094,10171,10240", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "4626,4710,8545,8645,8806,8972,9065,9156,9241,9312,9383,9465,9551,9796,10166,10235,10353"}}]}]}