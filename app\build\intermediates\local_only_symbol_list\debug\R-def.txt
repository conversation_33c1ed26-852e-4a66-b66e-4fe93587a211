R_DEF: Internal format may change without notice
local
color ar_measure_line
color ar_overlay
color ar_reticle
color ar_snap_indicator
color black
color measure_blue
color measure_blue_dark
color measure_green
color measure_green_dark
color measure_orange
color measure_red
color quality_excellent
color quality_fair
color quality_good
color quality_poor
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_launcher_placeholder
mipmap ic_launcher
mipmap ic_launcher_round
string action_calibrate
string action_cancel
string action_clear
string action_complete
string action_measure
string app_name
string calibration_complete
string calibration_instruction
string calibration_title
string error_ar_not_supported
string error_camera_not_available
string error_tracking_lost
string instruction_add_corner
string instruction_continue_path
string instruction_start_area
string instruction_start_path
string instruction_tap_first_point
string instruction_tap_second_point
string loading_ar
string loading_calibration
string measurement_area
string measurement_level
string measurement_line
string measurement_path
string measurement_volume
string permission_camera_description
string permission_camera_title
string permission_grant
string quality_excellent
string quality_fair
string quality_good
string quality_poor
string unit_cm
string unit_ft
string unit_in
string unit_m
string unit_m2
string unit_m3
style Base.Theme.Measure
style Theme.Measure
xml backup_rules
xml data_extraction_rules
