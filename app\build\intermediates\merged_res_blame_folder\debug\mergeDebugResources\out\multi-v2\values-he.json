{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-he/values-he.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values-he\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,236,283,403,524", "endColumns": "45,46,119,120,93", "endOffsets": "235,282,402,523,617"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,105,156,280,405", "endColumns": "49,50,123,124,97", "endOffsets": "100,151,275,400,498"}}]}]}