# Measure - AR Measurement App

A comprehensive Android AR measurement application that delivers sub-centimeter accuracy using ARCore, sensor fusion, and advanced computer vision techniques.

## Features

### Core Measurement Modes
- **Single-line measurement**: Point-to-point distance measurement
- **Multi-segment measurement**: Path measurement with multiple connected segments
- **Area calculation**: Polygon area measurement
- **Volume calculation**: 3D volume measurement
- **Level tool**: Digital bubble level with AR visualization

### Advanced Capabilities
- **Auto-feature detection**: Automatic edge and corner detection
- **Smart suggestions**: One-tap measurement of common objects
- **Sensor fusion**: Combines ARCore, IMU, and camera data for enhanced accuracy
- **Real-time quality indicator**: Live feedback on measurement reliability
- **Guided calibration**: Interactive device calibration process

### User Experience
- **Material 3 Design**: Modern, accessible interface with dynamic theming
- **Intuitive controls**: Gesture-driven measurement with haptic feedback
- **Visual guidance**: AR overlays with snap-to-surface indicators
- **Contextual help**: Progressive disclosure of features

## Technical Architecture

### AR Framework
- **ARCore**: Google's AR platform with Depth API integration
- **OpenGL ES 3.0**: High-performance 3D rendering
- **Camera2 API**: Advanced camera control and calibration

### Sensor Integration
- **IMU Fusion**: Accelerometer, gyroscope, and magnetometer data
- **Environmental sensing**: Light estimation and surface texture analysis
- **Calibration system**: Figure-8 motion calibration for optimal accuracy

### Machine Learning
- **Edge detection**: On-device TensorFlow Lite models
- **Surface classification**: Improved plane detection accuracy
- **Quality assessment**: Real-time measurement confidence scoring

### Performance Targets
- **60 FPS**: Smooth AR rendering on mid-range devices
- **Sub-centimeter accuracy**: ±0.5cm in optimal conditions
- **Real-time processing**: <50ms measurement latency

## Getting Started

### Prerequisites
- Android 7.0 (API level 24) or higher
- ARCore-compatible device
- Camera permission
- Minimum 3GB RAM recommended

### Installation
1. Clone the repository
2. Open in Android Studio
3. Sync Gradle dependencies
4. Run on ARCore-compatible device

### First Launch
1. Grant camera permissions
2. Complete device calibration
3. Follow the interactive tutorial
4. Start measuring!

## Usage

### Basic Measurement
1. Select measurement type from the toolbar
2. Tap to place first point
3. Tap to place second point (for line measurement)
4. View results with confidence interval

### Advanced Features
- **Calibration**: Tap calibrate button and move device in figure-8 pattern
- **Quality monitoring**: Watch the quality indicator in top-left corner
- **Multi-point measurement**: Continue tapping to add more points
- **Area measurement**: Close polygon by tapping near first point

## Accuracy & Quality

### Measurement Quality Factors
- **Lighting conditions**: Best in well-lit environments
- **Surface texture**: Textured surfaces provide better tracking
- **Distance**: Optimal range 0.5m - 5m from camera
- **Movement speed**: Slow, steady movements improve accuracy

### Quality Indicators
- **Poor**: Red indicator, ±2-5cm accuracy
- **Fair**: Yellow indicator, ±1-2cm accuracy  
- **Good**: Green indicator, ±0.5-1cm accuracy
- **Excellent**: Cyan indicator, ±0.2-0.5cm accuracy

## Development

### Project Structure
```
app/
├── src/main/java/com/measure/ar/
│   ├── ar/                 # ARCore integration
│   ├── data/              # Data models
│   ├── ui/                # Compose UI components
│   ├── viewmodel/         # MVVM architecture
│   └── MainActivity.kt    # Main entry point
├── src/main/res/          # Resources
└── build.gradle           # Dependencies
```

### Key Components
- **AREngine**: Core AR functionality and sensor fusion
- **MeasureViewModel**: State management and business logic
- **ARCameraView**: OpenGL rendering and camera integration
- **MeasurementToolbar**: UI controls and tool selection

### Testing
- Unit tests for measurement calculations
- Integration tests for AR functionality
- UI tests for user interactions
- Accuracy benchmarking against physical rulers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Google ARCore team for the AR framework
- Material Design team for UI guidelines
- TensorFlow team for ML capabilities
- Android Jetpack Compose team for modern UI toolkit
