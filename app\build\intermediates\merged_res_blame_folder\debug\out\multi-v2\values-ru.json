{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,889,981,1074,1169,1262,1358,1452,1548,1643,1735,1827,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,76,91,92,94,92,95,93,95,94,91,91,87,105,106,97,108,106,113,165,99,80", "endOffsets": "215,317,416,502,607,728,807,884,976,1069,1164,1257,1353,1447,1543,1638,1730,1822,1910,2016,2123,2221,2330,2437,2551,2717,2817,2898"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "553,668,770,869,955,1060,1181,1260,1337,1429,1522,1617,1710,1806,1900,1996,2091,2183,2275,2363,2469,2576,2674,2783,2890,3004,3170,9405", "endColumns": "114,101,98,85,104,120,78,76,91,92,94,92,95,93,95,94,91,91,87,105,106,97,108,106,113,165,99,80", "endOffsets": "663,765,864,950,1055,1176,1255,1332,1424,1517,1612,1705,1801,1895,1991,2086,2178,2270,2358,2464,2571,2669,2778,2885,2999,3165,3265,9481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,679,790,927,1040,1176,1256,1350,1439,1533,1644,1763,1869,1994,2118,2240,2416,2536,2655,2779,2896,2983,3079,3196,3325,3419,3529,3633,3760,3894,3997,4092,4173,4251,4341,4424,4528,4604,4684,4778,4875,4965,5056,5140,5242,5336,5430,5573,5649,5751", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "164,276,390,504,581,674,785,922,1035,1171,1251,1345,1434,1528,1639,1758,1864,1989,2113,2235,2411,2531,2650,2774,2891,2978,3074,3191,3320,3414,3524,3628,3755,3889,3992,4087,4168,4246,4336,4419,4523,4599,4679,4773,4870,4960,5051,5135,5237,5331,5425,5568,5644,5746,5839"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3270,3384,3496,3610,4627,4704,4797,4908,5045,5158,5294,5374,5468,5557,5651,5762,5881,5987,6112,6236,6358,6534,6654,6773,6897,7014,7101,7197,7314,7443,7537,7647,7751,7878,8012,8115,8410,8583,9315,9558,9742,10123,10199,10279,10373,10470,10560,10651,10735,10837,10931,11025,11168,11244,11346", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "3379,3491,3605,3719,4699,4792,4903,5040,5153,5289,5369,5463,5552,5646,5757,5876,5982,6107,6231,6353,6529,6649,6768,6892,7009,7096,7192,7309,7438,7532,7642,7746,7873,8007,8110,8205,8486,8656,9400,9636,9841,10194,10274,10368,10465,10555,10646,10730,10832,10926,11020,11163,11239,11341,11434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,285,410,535", "endColumns": "46,47,124,124,82", "endOffsets": "236,284,409,534,617"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,208,337,466", "endColumns": "50,51,128,128,86", "endOffsets": "151,203,332,461,548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3724,3822,3924,4025,4126,4231,4334,9641", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3817,3919,4020,4121,4226,4329,4446,9737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4451,4544,8210,8308,8491,8661,8743,8833,8921,9003,9074,9144,9228,9486,9846,9930,10000", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4539,4622,8303,8405,8578,8738,8828,8916,8998,9069,9139,9223,9310,9553,9925,9995,10118"}}]}]}