{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,9131", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,9216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b179e72bf03cadde01e4099557985561\\transformed\\material3-1.1.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,395,510,596,692,806,945,1067,1214,1295,1396,1488,1579,1691,1817,1923,2062,2196,2324,2513,2636,2761,2893,3018,3111,3203,3316,3434,3535,3636,3735,3872,4018,4121,4225,4296,4380,4470,4557,4658,4734,4815,4912,5014,5103,5200,5283,5387,5482,5580,5700,5776,5875", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "168,281,390,505,591,687,801,940,1062,1209,1290,1391,1483,1574,1686,1812,1918,2057,2191,2319,2508,2631,2756,2888,3013,3106,3198,3311,3429,3530,3631,3730,3867,4013,4116,4220,4291,4375,4465,4552,4653,4729,4810,4907,5009,5098,5195,5278,5382,5477,5575,5695,5771,5870,5960"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2869,2987,3100,3209,4243,4329,4425,4539,4678,4800,4947,5028,5129,5221,5312,5424,5550,5656,5795,5929,6057,6246,6369,6494,6626,6751,6844,6936,7049,7167,7268,7369,7468,7605,7751,7854,8150,8307,9041,9291,9479,9849,9925,10006,10103,10205,10294,10391,10474,10578,10673,10771,10891,10967,11066", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "2982,3095,3204,3319,4324,4420,4534,4673,4795,4942,5023,5124,5216,5307,5419,5545,5651,5790,5924,6052,6241,6364,6489,6621,6746,6839,6931,7044,7162,7263,7364,7463,7600,7746,7849,7953,8216,8386,9126,9373,9575,9920,10001,10098,10200,10289,10386,10469,10573,10668,10766,10886,10962,11061,11151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c919e02dd627ce1aec5be10fe930459e\\transformed\\core-1.10.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3324,3427,3529,3632,3737,3838,3940,9378", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3422,3524,3627,3732,3833,3935,4054,9474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3c15b22be3cd3ef6196522864286995\\transformed\\ui-1.4.3\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4059,4156,7958,8050,8221,8391,8468,8566,8654,8741,8811,8881,8959,9221,9580,9663,9730", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "4151,4238,8045,8145,8302,8463,8561,8649,8736,8806,8876,8954,9036,9286,9658,9725,9844"}}]}]}