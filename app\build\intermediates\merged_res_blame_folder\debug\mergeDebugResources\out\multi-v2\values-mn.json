{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "569,683,783,892,978,1084,1198,1281,1362,1453,1546,1641,1737,1834,1927,2021,2113,2204,2294,2374,2481,2584,2681,2788,2890,3003,3162,9418", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "678,778,887,973,1079,1193,1276,1357,1448,1541,1636,1732,1829,1922,2016,2108,2199,2289,2369,2476,2579,2676,2783,2885,2998,3157,3256,9494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,390,503,580,673,784,922,1036,1171,1252,1350,1438,1532,1646,1764,1867,2005,2145,2273,2445,2567,2684,2801,2918,3007,3103,3222,3356,3451,3555,3657,3796,3937,4040,4134,4213,4289,4370,4457,4554,4630,4709,4804,4900,4991,5089,5172,5276,5371,5471,5598,5674,5774", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "165,277,385,498,575,668,779,917,1031,1166,1247,1345,1433,1527,1641,1759,1862,2000,2140,2268,2440,2562,2679,2796,2913,3002,3098,3217,3351,3446,3550,3652,3791,3932,4035,4129,4208,4284,4365,4452,4549,4625,4704,4799,4895,4986,5084,5167,5271,5366,5466,5593,5669,5769,5860"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3261,3376,3488,3596,4622,4699,4792,4903,5041,5155,5290,5371,5469,5557,5651,5765,5883,5986,6124,6264,6392,6564,6686,6803,6920,7037,7126,7222,7341,7475,7570,7674,7776,7915,8056,8159,8441,8603,9337,9572,9760,10117,10193,10272,10367,10463,10554,10652,10735,10839,10934,11034,11161,11237,11337", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "3371,3483,3591,3704,4694,4787,4898,5036,5150,5285,5366,5464,5552,5646,5760,5878,5981,6119,6259,6387,6559,6681,6798,6915,7032,7121,7217,7336,7470,7565,7669,7771,7910,8051,8154,8248,8515,8674,9413,9654,9852,10188,10267,10362,10458,10549,10647,10730,10834,10929,11029,11156,11232,11332,11423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3709,3807,3909,4010,4108,4213,4325,9659", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3802,3904,4005,4103,4208,4320,4439,9755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,975,1046,1129,1212,1285,1362,1428", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,970,1041,1124,1207,1280,1357,1423,1540"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4444,4536,8253,8345,8520,8679,8765,8859,8946,9027,9100,9171,9254,9499,9857,9934,10000", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "4531,4617,8340,8436,8598,8760,8854,8941,9022,9095,9166,9249,9332,9567,9929,9995,10112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,237,292,415,545", "endColumns": "46,54,122,129,88", "endOffsets": "236,291,414,544,633"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,156,215,342,476", "endColumns": "50,58,126,133,92", "endOffsets": "151,210,337,471,564"}}]}]}