{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\144de6ed54e2a9f8bb523745bed0669e\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "564,672,768,874,959,1062,1180,1257,1333,1424,1517,1612,1706,1805,1898,1993,2092,2187,2281,2362,2469,2574,2671,2779,2882,2984,3138,9339", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "667,763,869,954,1057,1175,1252,1328,1419,1512,1607,1701,1800,1893,1988,2087,2182,2276,2357,2464,2569,2666,2774,2877,2979,3133,3231,9415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ccc457f66da22ed7e24b463509a2d4a\\transformed\\core-1.39.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,241,294,415,540", "endColumns": "50,52,120,124,88", "endOffsets": "240,293,414,539,628"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,160,217,342,471", "endColumns": "54,56,124,128,92", "endOffsets": "155,212,337,466,559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bed37921ac964737d07794cf4c9a434\\transformed\\material3-1.1.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,282,393,505,581,681,799,936,1060,1210,1291,1386,1472,1571,1682,1799,1899,2023,2144,2272,2434,2555,2673,2793,2919,3006,3101,3213,3335,3431,3536,3635,3767,3903,4005,4098,4171,4247,4328,4412,4513,4590,4669,4764,4858,4949,5043,5127,5226,5322,5420,5532,5609,5705", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "163,277,388,500,576,676,794,931,1055,1205,1286,1381,1467,1566,1677,1794,1894,2018,2139,2267,2429,2550,2668,2788,2914,3001,3096,3208,3330,3426,3531,3630,3762,3898,4000,4093,4166,4242,4323,4407,4508,4585,4664,4759,4853,4944,5038,5122,5221,5317,5415,5527,5604,5700,5792"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3236,3349,3463,3574,4601,4677,4777,4895,5032,5156,5306,5387,5482,5568,5667,5778,5895,5995,6119,6240,6368,6530,6651,6769,6889,7015,7102,7197,7309,7431,7527,7632,7731,7863,7999,8101,8392,8551,9258,9491,9676,10048,10125,10204,10299,10393,10484,10578,10662,10761,10857,10955,11067,11144,11240", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "3344,3458,3569,3681,4672,4772,4890,5027,5151,5301,5382,5477,5563,5662,5773,5890,5990,6114,6235,6363,6525,6646,6764,6884,7010,7097,7192,7304,7426,7522,7627,7726,7858,7994,8096,8189,8460,8622,9334,9570,9772,10120,10199,10294,10388,10479,10573,10657,10756,10852,10950,11062,11139,11235,11327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8d3a7436c8f640fe5c04b2f0f4278ea\\transformed\\ui-1.4.3\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4418,4514,8194,8291,8465,8627,8703,8794,8884,8970,9034,9099,9177,9420,9777,9858,9928", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "4509,4596,8286,8387,8546,8698,8789,8879,8965,9029,9094,9172,9253,9486,9853,9923,10043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68bbd61117eb14a617b740563c343275\\transformed\\core-1.10.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3686,3784,3886,3984,4082,4189,4298,9575", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3779,3881,3979,4077,4184,4293,4413,9671"}}]}]}