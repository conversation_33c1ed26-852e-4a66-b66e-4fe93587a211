{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-58:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aabceec2532be458503a11cdf665aba5\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "270,274", "startColumns": "4,4", "startOffsets": "17275,17452", "endColumns": "53,66", "endOffsets": "17324,17514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd7a17b97f8311dd73b1426bb32595b4\\transformed\\core-1.39.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "161,208,259,381,503", "endColumns": "46,50,121,121,83", "endOffsets": "207,258,380,502,586"}, "to": {"startLines": "308,309,310,311,312", "startColumns": "4,4,4,4,4", "startOffsets": "19296,19347,19402,19528,19654", "endColumns": "50,54,125,125,87", "endOffsets": "19342,19397,19523,19649,19737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3c15b22be3cd3ef6196522864286995\\transformed\\ui-1.4.3\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,263,264,265,267,269,302,361,362,399,400,402,407,408,423,424,425,426,434,435,438,442,443,444,1564,1567,1570", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14776,14835,14894,14954,15014,15074,15134,15194,15254,15314,15374,15434,15494,15553,15613,15673,15733,15793,15853,15913,15973,16033,16093,16153,16212,16272,16332,16391,16450,16509,16568,16627,16891,16965,17023,17135,17220,18930,23051,23116,25754,25820,25965,26330,26382,27364,27426,27480,27516,28000,28050,28210,28448,28495,28531,99761,99873,99984", "endLines": "227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,263,264,265,267,269,302,361,362,399,400,402,407,408,423,424,425,426,434,435,438,442,443,444,1566,1569,1573", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "14830,14889,14949,15009,15069,15129,15189,15249,15309,15369,15429,15489,15548,15608,15668,15728,15788,15848,15908,15968,16028,16088,16148,16207,16267,16327,16386,16445,16504,16563,16622,16681,16960,17018,17073,17181,17270,18978,23111,23165,25815,25916,26018,26377,26437,27421,27475,27511,27545,28045,28099,28251,28490,28526,28616,99868,99979,100174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\826cab2ae3bd23921f78dc7950cd571d\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "300", "startColumns": "4", "startOffsets": "18816", "endColumns": "49", "endOffsets": "18861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a763bb551790d89e4b176cfc4d4e654e\\transformed\\navigation-common-2.6.0\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3047,3060,3066,3072,3081", "startColumns": "4,4,4,4,4", "startOffsets": "168611,169250,169494,169741,170104", "endLines": "3059,3065,3071,3074,3085", "endColumns": "24,24,24,24,24", "endOffsets": "169245,169489,169736,169869,170281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\837385c73307431f9b8cea63829b3db1\\transformed\\navigation-runtime-2.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "273,2106,3075,3078", "startColumns": "4,4,4,4", "startOffsets": "17399,136842,169874,169989", "endLines": "273,2112,3077,3080", "endColumns": "52,24,24,24", "endOffsets": "17447,137141,169984,170099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b179e72bf03cadde01e4099557985561\\transformed\\material3-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "347,348,349,350,363,364,365,366,367,368,371,372,373,374,375,376,377,378,379,380,381,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,401,406,417,436,439,441,445,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21991,22075,22157,22234,23170,23218,23279,23358,23460,23542,23658,23708,23773,23830,23895,23980,24071,24141,24234,24323,24417,24562,24649,24733,24825,24919,24979,25043,25126,25216,25279,25347,25415,25512,25617,25689,25921,26284,27039,28104,28256,28380,28621,28667,28717,28784,28851,28917,28982,29036,29108,29175,29245,29327,29373,29439", "endLines": "347,348,349,350,363,364,365,366,367,370,371,372,373,374,375,376,377,378,379,380,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,401,406,417,436,439,441,445,446,447,448,449,450,451,452,453,454,455,456,457,458", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "22070,22152,22229,22309,23213,23274,23353,23455,23537,23653,23703,23768,23825,23890,23975,24066,24136,24229,24318,24412,24557,24644,24728,24820,24914,24974,25038,25121,25211,25274,25342,25410,25507,25612,25684,25749,25960,26325,27103,28152,28304,28443,28662,28712,28779,28846,28912,28977,29031,29103,29170,29240,29322,29368,29434,29495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68c34ecea127708eb8fec86a72bb1ff7\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "266,278,301,2798,2803", "startColumns": "4,4,4,4,4", "startOffsets": "17078,17657,18866,160967,161137", "endLines": "266,278,301,2802,2806", "endColumns": "56,64,63,24,24", "endOffsets": "17130,17717,18925,161132,161281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f31e21f052ed1ebd5de97e41508ed514\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,34,35,36,37,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,79,80,81,82,83,84,85,86,91,92,93,94,95,96,97,98,99,100,101,102,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,192,193,194,195,196,197,198,199,200,216,217,218,219,220,221,222,223,259,260,261,262,268,275,276,279,296,303,304,305,306,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,437,465,466,467,468,469,470,478,479,483,487,491,496,502,509,513,517,522,526,530,534,538,542,546,552,556,562,566,572,576,581,585,588,592,598,602,608,612,618,621,625,629,633,637,641,642,643,644,647,650,653,656,660,661,662,663,664,667,669,671,673,678,679,683,689,693,694,696,708,709,713,719,723,735,736,740,767,771,772,776,804,976,1002,1173,1199,1230,1238,1244,1260,1282,1287,1292,1302,1311,1320,1324,1331,1350,1357,1358,1367,1370,1373,1377,1381,1385,1388,1389,1394,1399,1409,1414,1421,1427,1428,1431,1435,1440,1442,1444,1447,1450,1452,1456,1459,1466,1469,1472,1476,1478,1482,1484,1486,1488,1492,1500,1508,1520,1526,1535,1538,1549,1552,1553,1558,1559,1574,1643,1713,1714,1724,1733,1734,1736,1740,1743,1746,1749,1752,1755,1758,1761,1765,1768,1771,1774,1778,1781,1785,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1811,1813,1814,1815,1816,1817,1818,1819,1820,1822,1823,1825,1826,1828,1830,1831,1833,1834,1835,1836,1837,1838,1840,1841,1842,1843,1844,1856,1858,1860,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1876,1877,1878,1879,1880,1881,1882,1884,1888,1893,1894,1895,1896,1897,1898,1902,1903,1904,1905,1907,1909,1911,1913,1915,1916,1917,1918,1920,1922,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1938,1939,1940,1941,1943,1945,1946,1948,1949,1951,1953,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1968,1969,1970,1971,1973,1974,1975,1976,1977,1979,1981,1983,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2002,2077,2080,2083,2086,2100,2113,2155,2158,2187,2214,2223,2287,2650,2660,2698,2726,2846,2870,2876,2882,2903,3027,3086,3092,3100,3106,3141,3173,3239,3259,3314,3326,3352", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,976,1040,1110,1171,1246,1322,1399,1837,1922,2004,2080,2198,2275,2353,2459,2565,2644,2724,2781,2970,3044,3119,3184,3250,3310,3371,3443,3516,3583,3651,3710,3769,3828,3887,3946,4000,4054,4107,4161,4215,4269,4762,4836,4915,4988,5062,5133,5205,5277,5551,5608,5666,5739,5813,5887,5962,6034,6107,6177,6248,6308,6411,6480,6549,6619,6693,6769,6833,6910,6986,7063,7128,7197,7274,7349,7418,7486,7563,7629,7690,7787,7852,7921,8020,8091,8150,8208,8265,8324,8388,8459,8531,8603,8675,8747,8814,8882,8950,9009,9072,9136,9226,9317,9377,9443,9510,9576,9646,9710,9763,9830,9891,9958,10071,10129,10192,10257,10322,10397,10470,10542,10586,10633,10679,10728,10789,10850,10911,10973,11037,11101,11165,11230,11293,11353,11414,11480,11539,11599,11661,11732,11792,12348,12434,12521,12611,12698,12786,12868,12951,13041,14110,14162,14220,14265,14331,14395,14452,14509,16686,16743,16791,16840,17186,17519,17566,17722,18627,18983,19047,19109,19169,19742,19816,19886,19964,20018,20088,20173,20221,20267,20328,20391,20457,20521,20592,20655,20720,20784,20845,20906,20958,21031,21105,21174,21249,21323,21397,21538,28157,29732,29810,29900,29988,30084,30174,30756,30845,31092,31373,31625,31910,32303,32780,33002,33224,33500,33727,33957,34187,34417,34647,34874,35293,35519,35944,36174,36602,36821,37104,37312,37443,37670,38096,38321,38748,38969,39394,39514,39790,40091,40415,40706,41020,41157,41288,41393,41635,41802,42006,42214,42485,42597,42709,42814,42931,43145,43291,43431,43517,43865,43953,44199,44617,44866,44948,45046,45703,45803,46055,46479,46734,47417,47506,47743,49767,50009,50111,50364,52520,63201,64717,75412,76940,78697,79323,79743,81004,82269,82525,82761,83308,83802,84407,84605,85185,86553,86928,87046,87584,87741,87937,88210,88466,88636,88777,88841,89206,89573,90249,90513,90851,91204,91298,91484,91790,92052,92177,92304,92543,92754,92873,93066,93243,93698,93879,94001,94260,94373,94560,94662,94769,94898,95173,95681,96177,97054,97348,97918,98067,98799,98971,99055,99391,99483,100179,105410,110781,110843,111421,112005,112096,112209,112438,112598,112750,112921,113087,113256,113423,113586,113829,113999,114172,114343,114617,114816,115021,115351,115435,115531,115627,115725,115825,115927,116029,116131,116233,116335,116435,116531,116643,116772,116895,117026,117157,117255,117369,117463,117603,117737,117833,117945,118045,118161,118257,118369,118469,118609,118745,118909,119039,119197,119347,119488,119632,119767,119879,120029,120157,120285,120421,120553,120683,120813,120925,121823,121969,122113,122251,122317,122407,122483,122587,122677,122779,122887,122995,123095,123175,123267,123365,123475,123527,123605,123711,123803,123907,124017,124139,124302,124521,124601,124701,124791,124901,124991,125232,125326,125432,125524,125624,125736,125850,125966,126082,126176,126290,126402,126504,126624,126746,126828,126932,127052,127178,127276,127370,127458,127570,127686,127808,127920,128095,128211,128297,128389,128501,128625,128692,128818,128886,129014,129158,129286,129355,129450,129565,129678,129777,129886,129997,130108,130209,130314,130414,130544,130635,130758,130852,130964,131050,131154,131250,131338,131456,131560,131664,131790,131878,131986,132086,132176,132286,132370,132472,132556,132610,132674,132780,132866,132976,133060,133319,135935,136053,136168,136248,136609,137146,138550,138628,139972,141333,141721,144564,154617,154955,156626,157983,162135,162886,163148,163348,163727,168005,170286,170515,170809,171024,172107,172957,175983,176727,178858,179198,180509", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,34,35,36,37,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,79,80,81,82,83,84,85,86,91,92,93,94,95,96,97,98,99,100,101,102,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,192,193,194,195,196,197,198,199,200,216,217,218,219,220,221,222,223,259,260,261,262,268,275,276,279,296,303,304,305,306,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,437,465,466,467,468,469,477,478,482,486,490,495,501,508,512,516,521,525,529,533,537,541,545,551,555,561,565,571,575,580,584,587,591,597,601,607,611,617,620,624,628,632,636,640,641,642,643,646,649,652,655,659,660,661,662,663,666,668,670,672,677,678,682,688,692,693,695,707,708,712,718,722,723,735,739,766,770,771,775,803,975,1001,1172,1198,1229,1237,1243,1259,1281,1286,1291,1301,1310,1319,1323,1330,1349,1356,1357,1366,1369,1372,1376,1380,1384,1387,1388,1393,1398,1408,1413,1420,1426,1427,1430,1434,1439,1441,1443,1446,1449,1451,1455,1458,1465,1468,1471,1475,1477,1481,1483,1485,1487,1491,1499,1507,1519,1525,1534,1537,1548,1551,1552,1557,1558,1563,1642,1712,1713,1723,1732,1733,1735,1739,1742,1745,1748,1751,1754,1757,1760,1764,1767,1770,1773,1777,1780,1784,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1810,1812,1813,1814,1815,1816,1817,1818,1819,1821,1822,1824,1825,1827,1829,1830,1832,1833,1834,1835,1836,1837,1839,1840,1841,1842,1843,1844,1857,1859,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1875,1876,1877,1878,1879,1880,1881,1883,1887,1891,1893,1894,1895,1896,1897,1901,1902,1903,1904,1906,1908,1910,1912,1914,1915,1916,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1937,1938,1939,1940,1942,1944,1945,1947,1948,1950,1952,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1967,1968,1969,1970,1972,1973,1974,1975,1976,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2076,2079,2082,2085,2099,2105,2122,2157,2186,2213,2222,2286,2649,2653,2687,2725,2743,2869,2875,2881,2902,3026,3046,3091,3095,3105,3140,3152,3238,3258,3313,3325,3351,3358", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,971,1035,1105,1166,1241,1317,1394,1472,1917,1999,2075,2151,2270,2348,2454,2560,2639,2719,2776,2834,3039,3114,3179,3245,3305,3366,3438,3511,3578,3646,3705,3764,3823,3882,3941,3995,4049,4102,4156,4210,4264,4318,4831,4910,4983,5057,5128,5200,5272,5345,5603,5661,5734,5808,5882,5957,6029,6102,6172,6243,6303,6364,6475,6544,6614,6688,6764,6828,6905,6981,7058,7123,7192,7269,7344,7413,7481,7558,7624,7685,7782,7847,7916,8015,8086,8145,8203,8260,8319,8383,8454,8526,8598,8670,8742,8809,8877,8945,9004,9067,9131,9221,9312,9372,9438,9505,9571,9641,9705,9758,9825,9886,9953,10066,10124,10187,10252,10317,10392,10465,10537,10581,10628,10674,10723,10784,10845,10906,10968,11032,11096,11160,11225,11288,11348,11409,11475,11534,11594,11656,11727,11787,11855,12429,12516,12606,12693,12781,12863,12946,13036,13127,14157,14215,14260,14326,14390,14447,14504,14558,16738,16786,16835,16886,17215,17561,17610,17763,18654,19042,19104,19164,19221,19811,19881,19959,20013,20083,20168,20216,20262,20323,20386,20452,20516,20587,20650,20715,20779,20840,20901,20953,21026,21100,21169,21244,21318,21392,21533,21603,28205,29805,29895,29983,30079,30169,30751,30840,31087,31368,31620,31905,32298,32775,32997,33219,33495,33722,33952,34182,34412,34642,34869,35288,35514,35939,36169,36597,36816,37099,37307,37438,37665,38091,38316,38743,38964,39389,39509,39785,40086,40410,40701,41015,41152,41283,41388,41630,41797,42001,42209,42480,42592,42704,42809,42926,43140,43286,43426,43512,43860,43948,44194,44612,44861,44943,45041,45698,45798,46050,46474,46729,46823,47501,47738,49762,50004,50106,50359,52515,63196,64712,75407,76935,78692,79318,79738,80999,82264,82520,82756,83303,83797,84402,84600,85180,86548,86923,87041,87579,87736,87932,88205,88461,88631,88772,88836,89201,89568,90244,90508,90846,91199,91293,91479,91785,92047,92172,92299,92538,92749,92868,93061,93238,93693,93874,93996,94255,94368,94555,94657,94764,94893,95168,95676,96172,97049,97343,97913,98062,98794,98966,99050,99386,99478,99756,105405,110776,110838,111416,112000,112091,112204,112433,112593,112745,112916,113082,113251,113418,113581,113824,113994,114167,114338,114612,114811,115016,115346,115430,115526,115622,115720,115820,115922,116024,116126,116228,116330,116430,116526,116638,116767,116890,117021,117152,117250,117364,117458,117598,117732,117828,117940,118040,118156,118252,118364,118464,118604,118740,118904,119034,119192,119342,119483,119627,119762,119874,120024,120152,120280,120416,120548,120678,120808,120920,121060,121964,122108,122246,122312,122402,122478,122582,122672,122774,122882,122990,123090,123170,123262,123360,123470,123522,123600,123706,123798,123902,124012,124134,124297,124454,124596,124696,124786,124896,124986,125227,125321,125427,125519,125619,125731,125845,125961,126077,126171,126285,126397,126499,126619,126741,126823,126927,127047,127173,127271,127365,127453,127565,127681,127803,127915,128090,128206,128292,128384,128496,128620,128687,128813,128881,129009,129153,129281,129350,129445,129560,129673,129772,129881,129992,130103,130204,130309,130409,130539,130630,130753,130847,130959,131045,131149,131245,131333,131451,131555,131659,131785,131873,131981,132081,132171,132281,132365,132467,132551,132605,132669,132775,132861,132971,133055,133175,135930,136048,136163,136243,136604,136837,137658,138623,139967,141328,141716,144559,154612,154747,156320,157978,158550,162881,163143,163343,163722,168000,168606,170510,170661,171019,172102,172414,175978,176722,178853,179193,180504,180707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a9e914c251df5cf90d7f25b691730a04\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2123,2139,2145,3153,3169", "startColumns": "4,4,4,4,4", "startOffsets": "137663,138088,138266,172419,172830", "endLines": "2138,2144,2154,3168,3172", "endColumns": "24,24,24,24,24", "endOffsets": "138083,138261,138545,172825,172952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\785459812721fef387d13b2ca62d5f88\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "297", "startColumns": "4", "startOffsets": "18659", "endColumns": "42", "endOffsets": "18697"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Measure Claude\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "39,37,38,36,40,2,28,27,26,51,52,53,17,15,16,14,12,13,56,57,7,9,5,6,8,32,31,33,23,21,22,20,43,46,45,44,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1847,1751,1800,1698,1902,55,1316,1225,1160,2234,2320,2399,830,672,749,591,423,506,2521,2579,236,340,136,186,286,1495,1416,1610,1074,982,1028,936,1977,2092,2053,2016,2131,2170", "endColumns": "54,48,46,52,50,44,69,90,64,85,78,95,68,76,80,80,82,84,57,62,49,51,49,49,53,114,78,61,55,45,45,45,38,38,38,36,38,38", "endOffsets": "1897,1795,1842,1746,1948,95,1381,1311,1220,2315,2394,2490,894,744,825,667,501,586,2574,2637,281,387,181,231,335,1605,1490,1667,1125,1023,1069,977,2011,2126,2087,2048,2165,2204"}, "to": {"startLines": "340,341,342,343,344,346,351,352,353,403,404,405,409,410,411,412,413,414,415,416,418,419,420,421,422,427,428,429,430,431,432,433,459,460,461,462,463,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21608,21663,21712,21759,21812,21946,22314,22384,22475,26023,26109,26188,26442,26511,26588,26669,26750,26833,26918,26976,27108,27158,27210,27260,27310,27550,27665,27744,27806,27862,27908,27954,29500,29539,29578,29617,29654,29693", "endColumns": "54,48,46,52,50,44,69,90,64,85,78,95,68,76,80,80,82,84,57,62,49,51,49,49,53,114,78,61,55,45,45,45,38,38,38,36,38,38", "endOffsets": "21658,21707,21754,21807,21858,21986,22379,22470,22535,26104,26183,26279,26506,26583,26664,26745,26828,26913,26971,27034,27153,27205,27255,27305,27359,27660,27739,27801,27857,27903,27949,27995,29534,29573,29612,29649,29688,29727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e08003d3ef9649394994765420dacc7\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "277,298", "startColumns": "4,4", "startOffsets": "17615,18702", "endColumns": "41,59", "endOffsets": "17652,18757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4d6d965193c8a894e382105422185963\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "299", "startColumns": "4", "startOffsets": "18762", "endColumns": "53", "endOffsets": "18811"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Measure Claude\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,15", "startColumns": "4,4", "startOffsets": "139,770", "endLines": "13,15", "endColumns": "12,62", "endOffsets": "764,828"}, "to": {"startLines": "724,1892", "startColumns": "4,4", "startOffsets": "46828,124459", "endLines": "734,1892", "endColumns": "12,61", "endOffsets": "47412,124516"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Measure Claude\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "16,14,15,17,2,6,7,9,10,11,8,23,21,22,20,3", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "608,514,561,660,55,176,225,327,377,432,279,904,806,855,757,97", "endColumns": "51,46,46,53,41,48,53,49,54,50,47,53,48,48,48,41", "endOffsets": "655,556,603,709,92,220,274,372,427,478,322,953,850,899,801,134"}, "to": {"startLines": "30,31,32,33,38,71,72,73,74,75,76,87,88,89,90,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1637,1689,1736,1783,2156,4323,4372,4426,4476,4531,4582,5350,5404,5453,5502,6369", "endColumns": "51,46,46,53,41,48,53,49,54,50,47,53,48,48,48,41", "endOffsets": "1684,1731,1778,1832,2193,4367,4421,4471,4526,4577,4625,5399,5448,5497,5546,6406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd9a1635caa2c1df36f22d42d5362ed6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "345", "startColumns": "4", "startOffsets": "21863", "endColumns": "82", "endOffsets": "21941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7659f7c52464abc48e5c105e92dee80\\transformed\\camera-view-1.2.3\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3096", "startColumns": "4,4,4", "startOffsets": "250,511,170666", "endLines": "7,17,3099", "endColumns": "11,11,24", "endOffsets": "397,813,170804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c919e02dd627ce1aec5be10fe930459e\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,47,48,77,78,185,186,187,188,189,190,191,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,224,225,226,271,272,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,307,354,355,356,357,358,359,360,440,1845,1846,1850,1851,1855,2000,2001,2654,2688,2744,2777,2807,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1477,1549,2839,2904,4630,4699,11860,11930,11998,12070,12140,12201,12275,13132,13193,13254,13316,13380,13442,13503,13571,13671,13731,13797,13870,13939,13996,14048,14563,14635,14711,17329,17364,17768,17823,17886,17941,17999,18057,18118,18181,18238,18289,18339,18400,18457,18523,18557,18592,19226,22540,22607,22679,22748,22817,22891,22963,28309,121065,121182,121383,121493,121694,133180,133252,154752,156325,158555,160286,161286,161968", "endLines": "9,28,29,47,48,77,78,185,186,187,188,189,190,191,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,224,225,226,271,272,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,307,354,355,356,357,358,359,360,440,1845,1849,1850,1854,1855,2000,2001,2659,2697,2776,2797,2839,2845", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1544,1632,2899,2965,4694,4757,11925,11993,12065,12135,12196,12270,12343,13188,13249,13311,13375,13437,13498,13566,13666,13726,13792,13865,13934,13991,14043,14105,14630,14706,14771,17359,17394,17818,17881,17936,17994,18052,18113,18176,18233,18284,18334,18395,18452,18518,18552,18587,18622,19291,22602,22674,22743,22812,22886,22958,23046,28375,121177,121378,121488,121689,121818,133247,133314,154950,156621,160281,160962,161963,162130"}}]}]}