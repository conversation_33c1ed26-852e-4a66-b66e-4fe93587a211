[{"merged": "com.measure.ar.app-debug-60:/xml_data_extraction_rules.xml.flat", "source": "com.measure.ar.app-main-62:/xml/data_extraction_rules.xml"}, {"merged": "com.measure.ar.app-debug-60:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.measure.ar.app-main-62:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.measure.ar.app-debug-60:/drawable_ic_launcher_placeholder.xml.flat", "source": "com.measure.ar.app-main-62:/drawable/ic_launcher_placeholder.xml"}, {"merged": "com.measure.ar.app-debug-60:/xml_backup_rules.xml.flat", "source": "com.measure.ar.app-main-62:/xml/backup_rules.xml"}, {"merged": "com.measure.ar.app-debug-60:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.measure.ar.app-main-62:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.measure.ar.app-debug-60:/drawable_ic_launcher_background.xml.flat", "source": "com.measure.ar.app-main-62:/drawable/ic_launcher_background.xml"}, {"merged": "com.measure.ar.app-debug-60:/drawable_ic_launcher_foreground.xml.flat", "source": "com.measure.ar.app-main-62:/drawable/ic_launcher_foreground.xml"}]