<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    <group android:scaleX="0.58"
           android:scaleY="0.58"
           android:translateX="22.68"
           android:translateY="22.68">
        <!-- Ruler icon for measurement app -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M20,4H4C2.9,4 2,4.9 2,6v12c0,1.1 0.9,2 2,2h16c1.1,0 2,-0.9 2,-2V6C22,4.9 21.1,4 20,4zM20,18H4V6h16V18z"/>
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M6,8h2v2H6V8z"/>
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M10,8h2v2h-2V8z"/>
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M14,8h2v2h-2V8z"/>
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M18,8h2v2h-2V8z"/>
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M6,12h4v2H6V12z"/>
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M14,12h4v2h-4V12z"/>
    </group>
</vector>
