{"logs": [{"outputFile": "com.measure.ar.app-mergeDebugResources-56:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00ff855e8f1c322d6d01ddd31185d1a4\\transformed\\core-1.10.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3724,3825,3927,4030,4134,4235,4340,9653", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3820,3922,4025,4129,4230,4335,4446,9749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dad28f5405e8ecd83a477663f8c93b32\\transformed\\material3-1.1.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,297,406,527,612,710,826,958,1075,1217,1298,1397,1484,1578,1686,1804,1908,2043,2176,2302,2462,2583,2694,2809,2922,3010,3105,3218,3346,3448,3549,3651,3784,3923,4029,4126,4198,4281,4365,4448,4549,4625,4705,4801,4898,4991,5085,5169,5269,5365,5462,5583,5659,5757", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "174,292,401,522,607,705,821,953,1070,1212,1293,1392,1479,1573,1681,1799,1903,2038,2171,2297,2457,2578,2689,2804,2917,3005,3100,3213,3341,3443,3544,3646,3779,3918,4024,4121,4193,4276,4360,4443,4544,4620,4700,4796,4893,4986,5080,5164,5264,5360,5457,5578,5654,5752,5846"}, "to": {"startLines": "34,35,36,37,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,83,92,95,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3252,3376,3494,3603,4639,4724,4822,4938,5070,5187,5329,5410,5509,5596,5690,5798,5916,6020,6155,6288,6414,6574,6695,6806,6921,7034,7122,7217,7330,7458,7560,7661,7763,7896,8035,8141,8436,8599,9331,9570,9754,10119,10195,10275,10371,10468,10561,10655,10739,10839,10935,11032,11153,11229,11327", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "3371,3489,3598,3719,4719,4817,4933,5065,5182,5324,5405,5504,5591,5685,5793,5911,6015,6150,6283,6409,6569,6690,6801,6916,7029,7117,7212,7325,7453,7555,7656,7758,7891,8030,8136,8233,8503,8677,9410,9648,9850,10190,10270,10366,10463,10556,10650,10734,10834,10930,11027,11148,11224,11322,11416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7dd1b3a9f23fc52e969f73e7935c2a62\\transformed\\appcompat-1.1.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,896,987,1079,1173,1272,1373,1466,1561,1655,1746,1838,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,78,90,91,93,98,100,92,94,93,90,91,83,104,105,99,108,104,101,157,105,82", "endOffsets": "210,311,421,509,616,730,812,891,982,1074,1168,1267,1368,1461,1556,1650,1741,1833,1917,2022,2128,2228,2337,2442,2544,2702,2808,2891"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "544,654,755,865,953,1060,1174,1256,1335,1426,1518,1612,1711,1812,1905,2000,2094,2185,2277,2361,2466,2572,2672,2781,2886,2988,3146,9415", "endColumns": "109,100,109,87,106,113,81,78,90,91,93,98,100,92,94,93,90,91,83,104,105,99,108,104,101,157,105,82", "endOffsets": "649,750,860,948,1055,1169,1251,1330,1421,1513,1607,1706,1807,1900,1995,2089,2180,2272,2356,2461,2567,2667,2776,2881,2983,3141,3247,9493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2f933205bace544606e07716f0ee6247\\transformed\\ui-1.4.3\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,994,1061,1142,1231,1303,1384,1450", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,989,1056,1137,1226,1298,1379,1445,1562"}, "to": {"startLines": "45,46,79,80,82,84,85,86,87,88,89,90,91,94,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4451,4551,8238,8335,8508,8682,8763,8851,8943,9025,9094,9161,9242,9498,9855,9936,10002", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "4546,4634,8330,8431,8594,8758,8846,8938,9020,9089,9156,9237,9326,9565,9931,9997,10114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08bdf27b58921855d050243aa9159418\\transformed\\core-1.39.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8", "startColumns": "0,0,0,0,0", "startOffsets": "190,240,293,405,523", "endColumns": "49,52,111,117,85", "endOffsets": "239,292,404,522,608"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "105,159,216,332,454", "endColumns": "53,56,115,121,89", "endOffsets": "154,211,327,449,539"}}]}]}